Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB23920000 ntdll.dll
7FFB22160000 KERNEL32.DLL
7FFB20F00000 KERNELBASE.dll
7FFB223B0000 USER32.dll
7FFB21410000 win32u.dll
000210040000 msys-2.0.dll
7FFB225C0000 GDI32.dll
7FFB212D0000 gdi32full.dll
7FFB21500000 msvcp_win.dll
7FFB20DB0000 ucrtbase.dll
7FFB21900000 advapi32.dll
7FFB227A0000 msvcrt.dll
7FFB21BE0000 sechost.dll
7FFB219C0000 RPCRT4.dll
7FFB201C0000 CRYPTBASE.DLL
7FFB215B0000 bcryptPrimitives.dll
7FFB218C0000 IMM32.DLL
