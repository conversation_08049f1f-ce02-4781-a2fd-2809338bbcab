{"ast": null, "code": "import { CalendarView } from 'angular-calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/authuser.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"angular-calendar\";\nfunction PlanningDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 31)(3, \"path\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.planning.lieu);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"span\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r6.username);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_38_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 39)(1, \"div\");\n    i0.ɵɵelement(2, \"strong\", 40);\n    i0.ɵɵpipe(3, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 3, event_r8.title), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(6, 5, event_r8.start, \"shortTime\"), \" - \", i0.ɵɵpipeBind2(7, 8, event_r8.end, \"shortTime\"), \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"hr\", 35);\n    i0.ɵɵelementStart(2, \"h3\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\", 37);\n    i0.ɵɵtemplate(6, PlanningDetailComponent_div_7_div_38_li_6_Template, 8, 11, \"li\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails pour le \", i0.ɵɵpipeBind2(4, 2, ctx_r5.selectedDate, \"fullDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.selectedDayEvents);\n  }\n}\nfunction PlanningDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"p\", 12);\n    i0.ɵɵpipe(4, \"highlightPresence\");\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"h2\", 14);\n    i0.ɵɵtext(7, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 16);\n    i0.ɵɵelement(10, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\", 18);\n    i0.ɵɵtext(12, \" Du \");\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" au \");\n    i0.ɵɵelementStart(17, \"strong\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(20, PlanningDetailComponent_div_7_div_20_Template, 6, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h2\", 14);\n    i0.ɵɵtext(23, \"Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 20);\n    i0.ɵɵtemplate(25, PlanningDetailComponent_div_7_div_25_Template, 3, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\")(27, \"div\", 22)(28, \"h2\", 23);\n    i0.ɵɵtext(29, \"R\\u00E9unions associ\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.nouvelleReunion());\n    });\n    i0.ɵɵtext(31, \" Nouvelle R\\u00E9union \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.editPlanning());\n    });\n    i0.ɵɵtext(34, \" Modifier Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deletePlanning());\n    });\n    i0.ɵɵtext(36, \" Supprimer Planning \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"mwl-calendar-month-view\", 28);\n    i0.ɵɵlistener(\"dayClicked\", function PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.handleDayClick($event.day));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, PlanningDetailComponent_div_7_div_38_Template, 7, 5, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.planning.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 9, ctx_r2.planning.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 11, ctx_r2.planning.dateDebut, \"mediumDate\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 14, ctx_r2.planning.dateFin, \"mediumDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.planning.lieu);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.planning.participants);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"viewDate\", ctx_r2.viewDate)(\"events\", ctx_r2.events);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDayEvents.length > 0);\n  }\n}\nexport class PlanningDetailComponent {\n  constructor(route, router, planningService, authService, cdr) {\n    this.route = route;\n    this.router = router;\n    this.planningService = planningService;\n    this.authService = authService;\n    this.cdr = cdr;\n    this.planning = null;\n    this.loading = true;\n    this.error = null;\n    this.isCreator = false;\n    this.selectedDayEvents = [];\n    this.selectedDate = null;\n    // Calendar setup\n    this.view = CalendarView.Month;\n    this.viewDate = new Date();\n    this.events = [];\n  }\n  ngOnInit() {\n    this.loadPlanningDetails();\n  }\n  loadPlanningDetails() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de planning non fourni';\n      this.loading = false;\n      return;\n    }\n    this.planningService.getPlanningById(id).subscribe({\n      next: planning => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n        this.events = this.planning.reunions.map(reunion => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false\n          };\n        });\n        this.cdr.detectChanges();\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n  handleDayClick(day) {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events; // These come from your `events` array\n  }\n\n  editPlanning() {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n  deletePlanning() {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => this.router.navigate(['/plannings']),\n        error: err => this.error = err.error?.message || 'Erreur lors de la suppression'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningDetailComponent_Factory(t) {\n      return new (t || PlanningDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningDetailComponent,\n      selectors: [[\"app-planning-detail\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-2\"], [1, \"text-gray-600\", \"mb-4\", 3, \"innerHTML\"], [1, \"mb-6\"], [1, \"text-lg\", \"font-semibold\", \"mb-3\", \"text-gray-800\"], [1, \"flex\", \"items-center\", \"mb-2\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"flex items-center bg-gray-100 rounded-full px-3 py-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-3\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded\", \"hover:bg-purple-700\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"justify-end\", \"space-x-2\", \"mb-3\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-red-500\", \"text-white\", \"rounded\", \"hover:bg-red-600\", \"transition-colors\", 3, \"click\"], [3, \"viewDate\", \"events\", \"dayClicked\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-full\", \"px-3\", \"py-1\"], [1, \"mt-4\"], [1, \"my-2\", \"border-gray-300\"], [1, \"text-md\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"p-2 border rounded bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\", \"border\", \"rounded\", \"bg-gray-50\"], [3, \"innerHTML\"]],\n      template: function PlanningDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function PlanningDetailComponent_Template_button_click_1_listener() {\n            return ctx.router.navigate([\"/plannings\"]);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(2, \"svg\", 2);\n          i0.ɵɵelement(3, \"path\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Retour aux plannings \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, PlanningDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n          i0.ɵɵtemplate(6, PlanningDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, PlanningDetailComponent_div_7_Template, 39, 17, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.planning);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.CalendarMonthViewComponent, i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWRldGFpbC9wbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CalendarView", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵnamespaceSVG", "ɵɵtextInterpolate", "ctx_r3", "planning", "lieu", "participant_r6", "username", "ɵɵproperty", "ɵɵpipeBind1", "event_r8", "title", "ɵɵsanitizeHtml", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "start", "end", "ɵɵtemplate", "PlanningDetailComponent_div_7_div_38_li_6_Template", "ctx_r5", "selectedDate", "selectedDayEvents", "PlanningDetailComponent_div_7_div_20_Template", "PlanningDetailComponent_div_7_div_25_Template", "ɵɵlistener", "PlanningDetailComponent_div_7_Template_button_click_30_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "nouvelleReunion", "PlanningDetailComponent_div_7_Template_button_click_33_listener", "ctx_r11", "editPlanning", "PlanningDetailComponent_div_7_Template_button_click_35_listener", "ctx_r12", "deletePlanning", "PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_37_listener", "$event", "ctx_r13", "handleDayClick", "day", "PlanningDetailComponent_div_7_div_38_Template", "ctx_r2", "titre", "description", "dateDebut", "dateFin", "participants", "viewDate", "events", "length", "PlanningDetailComponent", "constructor", "route", "router", "planningService", "authService", "cdr", "loading", "isCreator", "view", "Month", "Date", "ngOnInit", "loadPlanningDetails", "id", "snapshot", "paramMap", "get", "getPlanningById", "subscribe", "next", "<PERSON>ur", "_id", "getCurrentUserId", "reunions", "map", "reunion", "startStr", "date", "substring", "heureDebut", "endStr", "heure<PERSON>in", "allDay", "detectChanges", "err", "message", "console", "navigate", "confirm", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "PlanningService", "i3", "AuthuserService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "PlanningDetailComponent_Template", "rf", "ctx", "PlanningDetailComponent_Template_button_click_1_listener", "PlanningDetailComponent_div_5_Template", "PlanningDetailComponent_div_6_Template", "PlanningDetailComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-detail\\planning-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-detail\\planning-detail.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, OnInit} from '@angular/core';\n\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from '@app/services/authuser.service';\nimport { PlanningService } from '@app/services/planning.service';\nimport {\n  CalendarEvent, CalendarMonthViewDay,\n  CalendarView,\n} from 'angular-calendar';\n\n\n@Component({\n  selector: 'app-planning-detail',\n  templateUrl: './planning-detail.component.html',\n  styleUrls: ['./planning-detail.component.css']\n})\nexport class PlanningDetailComponent implements OnInit {\n\n  planning: any | null = null;\n  loading = true;\n  error: string | null = null;\n  isCreator = false;\n  selectedDayEvents: CalendarEvent[] = [];\n  selectedDate: Date | null = null;\n\n  // Calendar setup\n  view: CalendarView = CalendarView.Month;\n  viewDate: Date = new Date();\n  events: CalendarEvent[] = [];\n\n  constructor(\n    public route: ActivatedRoute,\n    public router: Router,\n    private planningService: PlanningService,\n    public authService: AuthuserService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPlanningDetails();\n  }\n\n  loadPlanningDetails(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de planning non fourni';\n      this.loading = false;\n      return;\n    }\n\n    this.planningService.getPlanningById(id).subscribe({\n      next: (planning: any) => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n\n        this.events = this.planning.reunions.map((reunion: any) => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false\n          };\n        });\n\n        this.cdr.detectChanges();\n      },\n      error: (err: any) => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n\n  handleDayClick(day: CalendarMonthViewDay): void {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events; // These come from your `events` array\n  }\n\n\n  editPlanning(): void {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n\n  deletePlanning(): void {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => this.router.navigate(['/plannings']),\n        error: (err) => this.error = err.error?.message || 'Erreur lors de la suppression'\n      });\n    }\n  }\n}", "<div class=\"container mx-auto px-4 py-6\">\n  <!-- Bouton retour -->\n  <button (click)=\"router.navigate(['/plannings'])\"\n          class=\"mb-4 flex items-center text-purple-600 hover:text-purple-800\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n    </svg>\n    Retour aux plannings\n  </button>\n\n  <!-- Chargement -->\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n  </div>\n\n  <!-- Erreur -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    {{ error }}\n  </div>\n\n  <!-- Détails du planning -->\n  <div *ngIf=\"!loading && planning\" class=\"bg-white rounded-lg shadow-md p-6\">\n    <!-- Titre du planning -->\n    <h1 class=\"text-2xl font-bold text-gray-800 mb-2\">{{ planning.titre }}</h1>\n    <p class=\"text-gray-600 mb-4\" [innerHTML]=\"planning.description | highlightPresence\"></p>\n\n    <!-- Informations -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-3 text-gray-800\">Informations</h2>\n      <div class=\"flex items-center mb-2\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span class=\"text-gray-700\">\n          Du <strong>{{ planning.dateDebut | date:'mediumDate' }}</strong>\n          au <strong>{{ planning.dateFin | date:'mediumDate' }}</strong>\n        </span>\n      </div>\n\n      <div *ngIf=\"planning.lieu\" class=\"flex items-center\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n        <span class=\"text-gray-700\">{{ planning.lieu }}</span>\n      </div>\n    </div>\n\n    <!-- Participants -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-3 text-gray-800\">Participants</h2>\n      <div class=\"flex flex-wrap gap-2\">\n        <div *ngFor=\"let participant of planning.participants\" class=\"flex items-center bg-gray-100 rounded-full px-3 py-1\">\n          <span class=\"text-gray-700\">{{ participant.username }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Réunions associées -->\n    <div>\n      <div class=\"flex justify-between items-center mb-3\">\n        <h2 class=\"text-lg font-semibold text-gray-800\">Réunions associées</h2>\n        <button (click)=\"nouvelleReunion()\" class=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors\">\n          Nouvelle Réunion\n        </button>\n      </div>\n\n      <!-- Boutons Modifier et Supprimer -->\n      <div class=\"flex justify-end space-x-2 mb-3\">\n        <button (click)=\"editPlanning()\" class=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\">\n          Modifier Planning\n        </button>\n        <button (click)=\"deletePlanning()\" class=\"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors\">\n          Supprimer Planning\n        </button>\n      </div>\n\n      <!-- Calendrier -->\n      <mwl-calendar-month-view\n        [viewDate]=\"viewDate\"\n        [events]=\"events\"\n        (dayClicked)=\"handleDayClick($event.day)\">\n      </mwl-calendar-month-view>\n\n      <!-- Détails des réunions du jour sélectionné -->\n      <div class=\"mt-4\" *ngIf=\"selectedDayEvents.length > 0\">\n        <hr class=\"my-2 border-gray-300\" />\n        <h3 class=\"text-md font-medium text-gray-700 mb-2\">\n          Détails pour le {{ selectedDate | date: 'fullDate' }}\n        </h3>\n        <ul class=\"space-y-2\">\n          <li *ngFor=\"let event of selectedDayEvents\" class=\"p-2 border rounded bg-gray-50\">\n            <div><strong [innerHTML]=\"event.title | highlightPresence\"></strong></div>\n            <div>\n              {{ event.start | date: 'shortTime' }} - {{ event.end | date: 'shortTime' }}\n            </div>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AAKA,SAEEA,YAAY,QACP,kBAAkB;;;;;;;;;;ICGvBC,EAAA,CAAAC,eAAA,EAA8C;IAA9CD,EAAA,CAAAE,cAAA,aAA8C;IAC5CF,EAAA,CAAAG,SAAA,aAA4F;IAC9FH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAGNJ,EAAA,CAAAC,eAAA,EAAgG;IAAhGD,EAAA,CAAAE,cAAA,aAAgG;IAC9FF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAqBIT,EAAA,CAAAE,cAAA,cAAqD;IACnDF,EAAA,CAAAU,cAAA,EAA8F;IAA9FV,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAG,SAAA,eAA+J;IAEjKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAK,MAAA,GAAmB;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAmB;;;;;IAQ/Cd,EAAA,CAAAE,cAAA,cAAoH;IACtFF,EAAA,CAAAK,MAAA,GAA0B;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAAjCJ,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAW,iBAAA,CAAAI,cAAA,CAAAC,QAAA,CAA0B;;;;;IAsCtDhB,EAAA,CAAAE,cAAA,aAAkF;IAC3EF,EAAA,CAAAG,SAAA,iBAA+D;;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1EJ,EAAA,CAAAE,cAAA,UAAK;IACHF,EAAA,CAAAK,MAAA,GACF;;;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IAHOJ,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAiB,UAAA,cAAAjB,EAAA,CAAAkB,WAAA,OAAAC,QAAA,CAAAC,KAAA,GAAApB,EAAA,CAAAqB,cAAA,CAA6C;IAExDrB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAsB,kBAAA,MAAAtB,EAAA,CAAAuB,WAAA,OAAAJ,QAAA,CAAAK,KAAA,uBAAAxB,EAAA,CAAAuB,WAAA,OAAAJ,QAAA,CAAAM,GAAA,oBACF;;;;;IAVNzB,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAG,SAAA,aAAmC;IACnCH,EAAA,CAAAE,cAAA,aAAmD;IACjDF,EAAA,CAAAK,MAAA,GACF;;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,aAAsB;IACpBF,EAAA,CAAA0B,UAAA,IAAAC,kDAAA,kBAKK;IACP3B,EAAA,CAAAI,YAAA,EAAK;;;;IATHJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAP,EAAA,CAAAuB,WAAA,OAAAK,MAAA,CAAAC,YAAA,mBACF;IAEwB7B,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiB,UAAA,YAAAW,MAAA,CAAAE,iBAAA,CAAoB;;;;;;;IAtElD9B,EAAA,CAAAC,eAAA,EAA4E;IAA5ED,EAAA,CAAAE,cAAA,cAA4E;IAExBF,EAAA,CAAAK,MAAA,GAAoB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IAC3EJ,EAAA,CAAAG,SAAA,YAAyF;;IAGzFH,EAAA,CAAAE,cAAA,cAAkB;IACqCF,EAAA,CAAAK,MAAA,mBAAY;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,cAAoC;IAClCF,EAAA,CAAAU,cAAA,EAA8F;IAA9FV,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAG,SAAA,gBAAmK;IACrKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,gBAA4B;IAC1BF,EAAA,CAAAK,MAAA,YAAG;IAAAL,EAAA,CAAAE,cAAA,cAAQ;IAAAF,EAAA,CAAAK,MAAA,IAA4C;;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAChEJ,EAAA,CAAAK,MAAA,YAAG;IAAAL,EAAA,CAAAE,cAAA,cAAQ;IAAAF,EAAA,CAAAK,MAAA,IAA0C;;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAIlEJ,EAAA,CAAA0B,UAAA,KAAAK,6CAAA,kBAMM;IACR/B,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,oBAAY;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,eAAkC;IAChCF,EAAA,CAAA0B,UAAA,KAAAM,6CAAA,kBAEM;IACRhC,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,WAAK;IAE+CF,EAAA,CAAAK,MAAA,oCAAkB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACvEJ,EAAA,CAAAE,cAAA,kBAA6H;IAArHF,EAAA,CAAAiC,UAAA,mBAAAC,gEAAA;MAAAlC,EAAA,CAAAmC,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAArC,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACjCxC,EAAA,CAAAK,MAAA,+BACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAIXJ,EAAA,CAAAE,cAAA,eAA6C;IACnCF,EAAA,CAAAiC,UAAA,mBAAAQ,gEAAA;MAAAzC,EAAA,CAAAmC,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAA1C,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9B3C,EAAA,CAAAK,MAAA,2BACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAsH;IAA9GF,EAAA,CAAAiC,UAAA,mBAAAW,gEAAA;MAAA5C,EAAA,CAAAmC,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAA7C,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAM,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAChC9C,EAAA,CAAAK,MAAA,4BACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAIXJ,EAAA,CAAAE,cAAA,mCAG4C;IAA1CF,EAAA,CAAAiC,UAAA,wBAAAc,sFAAAC,MAAA;MAAAhD,EAAA,CAAAmC,aAAA,CAAAC,IAAA;MAAA,MAAAa,OAAA,GAAAjD,EAAA,CAAAsC,aAAA;MAAA,OAActC,EAAA,CAAAuC,WAAA,CAAAU,OAAA,CAAAC,cAAA,CAAAF,MAAA,CAAAG,GAAA,CAA0B;IAAA,EAAC;IAC3CnD,EAAA,CAAAI,YAAA,EAA0B;IAG1BJ,EAAA,CAAA0B,UAAA,KAAA0B,6CAAA,kBAaM;IACRpD,EAAA,CAAAI,YAAA,EAAM;;;;IA5E4CJ,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAW,iBAAA,CAAA0C,MAAA,CAAAxC,QAAA,CAAAyC,KAAA,CAAoB;IACxCtD,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAiB,UAAA,cAAAjB,EAAA,CAAAkB,WAAA,OAAAmC,MAAA,CAAAxC,QAAA,CAAA0C,WAAA,GAAAvD,EAAA,CAAAqB,cAAA,CAAsD;IAUnErB,EAAA,CAAAM,SAAA,IAA4C;IAA5CN,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAuB,WAAA,SAAA8B,MAAA,CAAAxC,QAAA,CAAA2C,SAAA,gBAA4C;IAC5CxD,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAuB,WAAA,SAAA8B,MAAA,CAAAxC,QAAA,CAAA4C,OAAA,gBAA0C;IAInDzD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAiB,UAAA,SAAAoC,MAAA,CAAAxC,QAAA,CAAAC,IAAA,CAAmB;IAaMd,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAiB,UAAA,YAAAoC,MAAA,CAAAxC,QAAA,CAAA6C,YAAA,CAAwB;IA2BrD1D,EAAA,CAAAM,SAAA,IAAqB;IAArBN,EAAA,CAAAiB,UAAA,aAAAoC,MAAA,CAAAM,QAAA,CAAqB,WAAAN,MAAA,CAAAO,MAAA;IAMJ5D,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAiB,UAAA,SAAAoC,MAAA,CAAAvB,iBAAA,CAAA+B,MAAA,KAAkC;;;ADrE3D,OAAM,MAAOC,uBAAuB;EAclCC,YACSC,KAAqB,EACrBC,MAAc,EACbC,eAAgC,EACjCC,WAA4B,EAC3BC,GAAsB;IAJvB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,GAAG,GAAHA,GAAG;IAjBb,KAAAvD,QAAQ,GAAe,IAAI;IAC3B,KAAAwD,OAAO,GAAG,IAAI;IACd,KAAA5D,KAAK,GAAkB,IAAI;IAC3B,KAAA6D,SAAS,GAAG,KAAK;IACjB,KAAAxC,iBAAiB,GAAoB,EAAE;IACvC,KAAAD,YAAY,GAAgB,IAAI;IAEhC;IACA,KAAA0C,IAAI,GAAiBxE,YAAY,CAACyE,KAAK;IACvC,KAAAb,QAAQ,GAAS,IAAIc,IAAI,EAAE;IAC3B,KAAAb,MAAM,GAAoB,EAAE;EAQzB;EAEHc,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,EAAE,GAAG,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAACnE,KAAK,GAAG,2BAA2B;MACxC,IAAI,CAAC4D,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACH,eAAe,CAACc,eAAe,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MACjDC,IAAI,EAAGrE,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;QACjC,IAAI,CAACyD,SAAS,GAAGzD,QAAQ,CAACA,QAAQ,CAACsE,QAAQ,CAACC,GAAG,KAAK,IAAI,CAACjB,WAAW,CAACkB,gBAAgB,EAAE;QACvF,IAAI,CAAChB,OAAO,GAAG,KAAK;QAEpB,IAAI,CAACT,MAAM,GAAG,IAAI,CAAC/C,QAAQ,CAACyE,QAAQ,CAACC,GAAG,CAAEC,OAAY,IAAI;UACxD,MAAMC,QAAQ,GAAG,GAAGD,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACI,UAAU,KAAK;UAC5E,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACM,QAAQ,KAAK;UAExE,OAAO;YACLtE,KAAK,EAAE,IAAIiD,IAAI,CAACgB,QAAQ,CAAC;YACzBhE,GAAG,EAAE,IAAIgD,IAAI,CAACoB,MAAM,CAAC;YACrBzE,KAAK,EAAEoE,OAAO,CAAClC,KAAK;YACpByC,MAAM,EAAE;WACT;QACH,CAAC,CAAC;QAEF,IAAI,CAAC3B,GAAG,CAAC4B,aAAa,EAAE;MAC1B,CAAC;MACDvF,KAAK,EAAGwF,GAAQ,IAAI;QAClB,IAAI,CAACxF,KAAK,GAAGwF,GAAG,CAACxF,KAAK,EAAEyF,OAAO,IAAI,2BAA2B;QAC9D,IAAI,CAAC7B,OAAO,GAAG,KAAK;QACpB8B,OAAO,CAAC1F,KAAK,CAAC,SAAS,EAAEwF,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEA/C,cAAcA,CAACC,GAAyB;IACtC,IAAI,CAACtB,YAAY,GAAGsB,GAAG,CAACuC,IAAI;IAC5B,IAAI,CAAC5D,iBAAiB,GAAGqB,GAAG,CAACS,MAAM,CAAC,CAAC;EACvC;;EAGAjB,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC9B,QAAQ,EAAE;MACjB,IAAI,CAACoD,MAAM,CAACmC,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAACvF,QAAQ,CAACuE,GAAG,CAAC,CAAC;;EAEhE;EAEAtC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACjC,QAAQ,IAAIwF,OAAO,CAAC,wCAAwC,CAAC,EAAE;MACtE,IAAI,CAACnC,eAAe,CAACpB,cAAc,CAAC,IAAI,CAACjC,QAAQ,CAACuE,GAAG,CAAC,CAACH,SAAS,CAAC;QAC/DC,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACjB,MAAM,CAACmC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QAChD3F,KAAK,EAAGwF,GAAG,IAAK,IAAI,CAACxF,KAAK,GAAGwF,GAAG,CAACxF,KAAK,EAAEyF,OAAO,IAAI;OACpD,CAAC;;EAEN;;;uBAjFWpC,uBAAuB,EAAA9D,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzG,EAAA,CAAAsG,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA3G,EAAA,CAAAsG,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAA7G,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAA8G,iBAAA;IAAA;EAAA;;;YAAvBhD,uBAAuB;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBpCrH,EAAA,CAAAE,cAAA,aAAyC;UAE/BF,EAAA,CAAAiC,UAAA,mBAAAsF,yDAAA;YAAA,OAASD,GAAA,CAAArD,MAAA,CAAAmC,QAAA,EAAiB,YAAY,EAAE;UAAA,EAAC;UAE/CpG,EAAA,CAAAU,cAAA,EAAqG;UAArGV,EAAA,CAAAE,cAAA,aAAqG;UACnGF,EAAA,CAAAG,SAAA,cAA0L;UAC5LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,MAAA,6BACF;UAAAL,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAA0B,UAAA,IAAA8F,sCAAA,iBAEM;UAGNxH,EAAA,CAAA0B,UAAA,IAAA+F,sCAAA,iBAEM;UAGNzH,EAAA,CAAA0B,UAAA,IAAAgG,sCAAA,mBA+EM;UACR1H,EAAA,CAAAI,YAAA,EAAM;;;UA1FEJ,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAiB,UAAA,SAAAqG,GAAA,CAAAjD,OAAA,CAAa;UAKbrE,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAiB,UAAA,SAAAqG,GAAA,CAAA7G,KAAA,CAAW;UAKXT,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAiB,UAAA,UAAAqG,GAAA,CAAAjD,OAAA,IAAAiD,GAAA,CAAAzG,QAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}