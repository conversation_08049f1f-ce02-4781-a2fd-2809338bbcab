{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/planning.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction PlanningFormComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction PlanningFormComponent_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Titre is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"At least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, PlanningFormComponent_div_13_span_1_Template, 2, 0, \"span\", 23);\n    i0.ɵɵtemplate(2, PlanningFormComponent_div_13_span_2_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningFormComponent_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r7.username);\n  }\n}\nfunction PlanningFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Please select at least one participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent__svg_svg_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 25);\n    i0.ɵɵelement(1, \"circle\", 26)(2, \"path\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PlanningFormComponent {\n  constructor(fb, userService, planningService, router) {\n    this.fb = fb;\n    this.userService = userService;\n    this.planningService = planningService;\n    this.router = router;\n    this.isLoading = false;\n    this.users$ = this.userService.getAllUsers();\n  }\n  ngOnInit() {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      lieu: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      participants: [[], Validators.required]\n    });\n  }\n  submit() {\n    if (this.planningForm.valid) {\n      this.isLoading = true;\n      const planning = this.planningForm.value;\n      // Call the createPlanning method to add the new planning\n      this.planningService.createPlanning(planning).subscribe(newPlanning => {\n        console.log('Planning created:', newPlanning);\n        this.isLoading = false;\n        // Optionally, reset the form or navigate to another page after successful creation\n        this.planningForm.reset();\n        this.router.navigate(['plannings']);\n      }, error => {\n        console.error('Error creating planning:', error);\n        this.isLoading = false;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningFormComponent_Factory(t) {\n      return new (t || PlanningFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningFormComponent,\n      selectors: [[\"app-planning-form\"]],\n      decls: 41,\n      vars: 12,\n      consts: [[1, \"bg-gray-50\", \"p-5\", \"sm:p-6\", \"rounded-xl\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-y-4\", \"gap-x-4\", \"sm:grid-cols-6\"], [1, \"sm:col-span-3\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"class\", \"text-sm text-red-600 mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"sm:col-span-6\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"ml-3\", \"inline-flex\", \"justify-center\", \"py-2\", \"px-4\", \"border\", \"border-transparent\", \"shadow-sm\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-purple-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\"], [\"class\", \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mb-4\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"text-sm\", \"text-red-600\", \"mt-1\"], [4, \"ngIf\"], [3, \"value\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n      template: function PlanningFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Planning Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Fill in the details for the event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function PlanningFormComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.submit();\n          });\n          i0.ɵɵtemplate(7, PlanningFormComponent_div_7_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"label\", 8);\n          i0.ɵɵtext(11, \"Titre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 9);\n          i0.ɵɵtemplate(13, PlanningFormComponent_div_13_Template, 3, 2, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"label\", 8);\n          i0.ɵɵtext(20, \"Date de d\\u00E9but\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 7)(23, \"label\", 8);\n          i0.ɵɵtext(24, \"Date de fin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"label\", 8);\n          i0.ɵɵtext(28, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"select\", 15);\n          i0.ɵɵtemplate(30, PlanningFormComponent_option_30_Template, 2, 2, \"option\", 16);\n          i0.ɵɵpipe(31, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, PlanningFormComponent_div_32_Template, 2, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 14)(34, \"label\", 8);\n          i0.ɵɵtext(35, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"textarea\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 18)(38, \"button\", 19);\n          i0.ɵɵtemplate(39, PlanningFormComponent__svg_svg_39_Template, 3, 0, \"svg\", 20);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"border-red-300\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(31, 10, ctx.users$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.planningForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : \"Save Planning\", \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1mb3JtLmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWZvcm0vcGxhbm5pbmctZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "errorMessage", "ɵɵtemplate", "PlanningFormComponent_div_13_span_1_Template", "PlanningFormComponent_div_13_span_2_Template", "ɵɵproperty", "tmp_0_0", "ctx_r1", "planningForm", "get", "errors", "tmp_1_0", "user_r7", "_id", "ɵɵtextInterpolate", "username", "ɵɵnamespaceSVG", "ɵɵelement", "PlanningFormComponent", "constructor", "fb", "userService", "planningService", "router", "isLoading", "users$", "getAllUsers", "ngOnInit", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "lieu", "dateDebut", "dateFin", "participants", "submit", "valid", "planning", "value", "createPlanning", "subscribe", "newPlanning", "console", "log", "reset", "navigate", "error", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "DataService", "i3", "PlanningService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "PlanningFormComponent_Template", "rf", "ctx", "ɵɵlistener", "PlanningFormComponent_Template_form_ngSubmit_6_listener", "PlanningFormComponent_div_7_Template", "PlanningFormComponent_div_13_Template", "PlanningFormComponent_option_30_Template", "PlanningFormComponent_div_32_Template", "PlanningFormComponent__svg_svg_39_Template", "ɵɵclassProp", "tmp_2_0", "invalid", "touched", "tmp_3_0", "ɵɵpipeBind1", "tmp_5_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-form\\planning-form.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-form\\planning-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {FormB<PERSON>er, FormGroup, Validators} from \"@angular/forms\";\nimport {Observable} from \"rxjs\";\nimport {User} from \"@app/models/user.model\";\nimport {DataService} from \"@app/services/data.service\";\nimport {Planning} from \"@app/models/planning.model\";\nimport {PlanningService} from \"@app/services/planning.service\";\nimport {Router} from \"@angular/router\";\n\n@Component({\n  selector: 'app-planning-form',\n  templateUrl: './planning-form.component.html',\n  styleUrls: ['./planning-form.component.css']\n})\nexport class PlanningFormComponent implements OnInit {\n  planningForm!: FormGroup;\n  isLoading = false;\n  users$: Observable<User[]> = this.userService.getAllUsers();\n\n  constructor(\n    private fb: FormBuilder,\n    private userService: DataService,\n    private planningService: PlanningService,\n    private router:Router\n  ) {}\n\n  ngOnInit(): void {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      lieu: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      participants: [[], Validators.required]\n    });\n  }\n\n  submit(): void {\n    if (this.planningForm.valid) {\n      this.isLoading = true;\n      const planning: Planning = this.planningForm.value;\n\n      // Call the createPlanning method to add the new planning\n      this.planningService.createPlanning(planning).subscribe(\n        (newPlanning:any) => {\n          console.log('Planning created:', newPlanning);\n          this.isLoading = false;\n          // Optionally, reset the form or navigate to another page after successful creation\n          this.planningForm.reset();\n          this.router.navigate(['plannings'])\n        },\n        (error:any) => {\n          console.error('Error creating planning:', error);\n          this.isLoading = false;\n        }\n      );\n    }\n  }\n}", "<section class=\"bg-gray-50 p-5 sm:p-6 rounded-xl\">\n  <div class=\"mb-4\">\n    <h2 class=\"text-lg font-medium text-gray-900\">Planning Form</h2>\n    <p class=\"text-sm text-gray-500 mt-1\">Fill in the details for the event</p>\n  </div>\n\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"submit()\" novalidate>\n    <!-- Error message -->\n    <div *ngIf=\"errorMessage\" class=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n      {{ errorMessage }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6\">\n      <!-- Titre -->\n      <div class=\"sm:col-span-3\">\n        <label class=\"block text-sm font-medium text-gray-700\">Titre</label>\n        <input\n          type=\"text\"\n          formControlName=\"titre\"\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n          [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\n        />\n        <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-sm text-red-600 mt-1\">\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Titre is required</span>\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">At least 3 characters</span>\n        </div>\n      </div>\n\n      <!-- Lieu -->\n      <div class=\"sm:col-span-3\">\n        <label class=\"block text-sm font-medium text-gray-700\">Lieu</label>\n        <input\n          type=\"text\"\n          formControlName=\"lieu\"\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n        />\n      </div>\n\n      <!-- Date début -->\n      <div class=\"sm:col-span-3\">\n        <label class=\"block text-sm font-medium text-gray-700\">Date de début</label>\n        <input\n          type=\"date\"\n          formControlName=\"dateDebut\"\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n        />\n      </div>\n\n      <!-- Date fin -->\n      <div class=\"sm:col-span-3\">\n        <label class=\"block text-sm font-medium text-gray-700\">Date de fin</label>\n        <input\n          type=\"date\"\n          formControlName=\"dateFin\"\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n        />\n      </div>\n\n      <!-- Participants -->\n      <div class=\"sm:col-span-6\">\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\n        <select\n          formControlName=\"participants\"\n          multiple\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n        >\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\">{{ user.username }}</option>\n        </select>\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-sm text-red-600 mt-1\">\n          Please select at least one participant\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div class=\"sm:col-span-6\">\n        <label class=\"block text-sm font-medium text-gray-700\">Description</label>\n        <textarea\n          formControlName=\"description\"\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\n          rows=\"3\"\n        ></textarea>\n      </div>\n    </div>\n\n    <div class=\"mt-6 flex justify-end\">\n      <button\n        type=\"submit\"\n        [disabled]=\"planningForm.invalid || isLoading\"\n        class=\"ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n      >\n        <svg\n          *ngIf=\"isLoading\"\n          class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            class=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            stroke-width=\"4\"\n          ></circle>\n          <path\n            class=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n        {{ isLoading ? 'Saving...' : 'Save Planning' }}\n      </button>\n    </div>\n  </form>\n</section>"], "mappings": "AACA,SAAgCA,UAAU,QAAO,gBAAgB;;;;;;;;;ICO7DC,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACF;;;;;IAaMP,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF7FH,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAQ,UAAA,IAAAC,4CAAA,mBAAsF;IACtFT,EAAA,CAAAQ,UAAA,IAAAE,4CAAA,mBAA2F;IAC7FV,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAW,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDhB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAW,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IA0C7DhB,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAW,UAAA,UAAAO,OAAA,CAAAC,GAAA,CAAkB;IAACnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAoB,iBAAA,CAAAF,OAAA,CAAAG,QAAA,CAAmB;;;;;IAEpFrB,EAAA,CAAAC,cAAA,cAAsI;IACpID,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAsB,cAAA,EAMC;IANDtB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAuB,SAAA,iBAOU;IAMZvB,EAAA,CAAAG,YAAA,EAAM;;;ADhGd,OAAM,MAAOqB,qBAAqB;EAKhCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,eAAgC,EAChCC,MAAa;IAHb,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAuB,IAAI,CAACJ,WAAW,CAACK,WAAW,EAAE;EAOxD;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACnB,YAAY,GAAG,IAAI,CAACY,EAAE,CAACQ,KAAK,CAAC;MAChCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAACqC,QAAQ,EAAErC,UAAU,CAACsC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,EAAEzC,UAAU,CAACqC,QAAQ,CAAC;MACpCK,OAAO,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAACqC,QAAQ,CAAC;MAClCM,YAAY,EAAE,CAAC,EAAE,EAAE3C,UAAU,CAACqC,QAAQ;KACvC,CAAC;EACJ;EAEAO,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC7B,YAAY,CAAC8B,KAAK,EAAE;MAC3B,IAAI,CAACd,SAAS,GAAG,IAAI;MACrB,MAAMe,QAAQ,GAAa,IAAI,CAAC/B,YAAY,CAACgC,KAAK;MAElD;MACA,IAAI,CAAClB,eAAe,CAACmB,cAAc,CAACF,QAAQ,CAAC,CAACG,SAAS,CACpDC,WAAe,IAAI;QAClBC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,WAAW,CAAC;QAC7C,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB;QACA,IAAI,CAAChB,YAAY,CAACsC,KAAK,EAAE;QACzB,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC,EACAC,KAAS,IAAI;QACZJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACxB,SAAS,GAAG,KAAK;MACxB,CAAC,CACF;;EAEL;;;uBA3CWN,qBAAqB,EAAAxB,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzD,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3D,EAAA,CAAAuD,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA7D,EAAA,CAAAuD,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBvC,qBAAqB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCtE,EAAA,CAAAC,cAAA,iBAAkD;UAEAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,WAAsC;UAAAD,EAAA,CAAAE,MAAA,wCAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7EH,EAAA,CAAAC,cAAA,cAAkE;UAAjCD,EAAA,CAAAwE,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAA5B,MAAA,EAAQ;UAAA,EAAC;UAEpD3C,EAAA,CAAAQ,UAAA,IAAAkE,oCAAA,iBAEM;UAEN1E,EAAA,CAAAC,cAAA,aAA6D;UAGFD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAuB,SAAA,gBAKE;UACFvB,EAAA,CAAAQ,UAAA,KAAAmE,qCAAA,kBAGM;UACR3E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAuB,SAAA,iBAIE;UACJvB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAuB,SAAA,iBAIE;UACJvB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAuB,SAAA,iBAIE;UACJvB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAQ,UAAA,KAAAoE,wCAAA,qBAA2F;;UAC7F5E,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAQ,UAAA,KAAAqE,qCAAA,kBAEM;UACR7E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAuB,SAAA,oBAIY;UACdvB,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAmC;UAM/BD,EAAA,CAAAQ,UAAA,KAAAsE,0CAAA,kBAoBM;UACN9E,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;UA1GPH,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAW,UAAA,cAAA4D,GAAA,CAAAzD,YAAA,CAA0B;UAExBd,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAW,UAAA,SAAA4D,GAAA,CAAAhE,YAAA,CAAkB;UAYlBP,EAAA,CAAAI,SAAA,GAAiG;UAAjGJ,EAAA,CAAA+E,WAAA,qBAAAC,OAAA,GAAAT,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAT,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAiE,OAAA,CAAAE,OAAA,EAAiG;UAE7FlF,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAW,UAAA,WAAAwE,OAAA,GAAAZ,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAZ,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAAD,OAAA,EAA8E;UA4CzDlF,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAoF,WAAA,SAAAb,GAAA,CAAAxC,MAAA,EAAiB;UAEtC/B,EAAA,CAAAI,SAAA,GAA4F;UAA5FJ,EAAA,CAAAW,UAAA,WAAA0E,OAAA,GAAAd,GAAA,CAAAzD,YAAA,CAAAC,GAAA,mCAAAsE,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAd,GAAA,CAAAzD,YAAA,CAAAC,GAAA,mCAAAsE,OAAA,CAAAH,OAAA,EAA4F;UAmBlGlF,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAW,UAAA,aAAA4D,GAAA,CAAAzD,YAAA,CAAAmE,OAAA,IAAAV,GAAA,CAAAzC,SAAA,CAA8C;UAI3C9B,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAW,UAAA,SAAA4D,GAAA,CAAAzC,SAAA,CAAe;UAoBlB9B,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAkE,GAAA,CAAAzC,SAAA,sCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}