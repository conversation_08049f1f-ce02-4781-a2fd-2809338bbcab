{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/reunion.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/common\";\nfunction ReunionListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Erreur lors du chargement des r\\u00E9unions: \", ctx_r1.error.message, \" \");\n  }\n}\nfunction ReunionListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 9);\n    i0.ɵɵelement(2, \"path\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 11);\n    i0.ɵɵtext(4, \"Aucune r\\u00E9union pr\\u00E9vue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 12);\n    i0.ɵɵtext(6, \"Commencez par planifier une nouvelle r\\u00E9union.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_20_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r9 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", participant_r9.username, \" (\", participant_r9.email, \")\");\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"strong\");\n    i0.ɵɵtext(2, \"Participants:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 30);\n    i0.ɵɵtemplate(4, ReunionListComponent_div_7_div_1_div_20_li_4_Template, 2, 2, \"li\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", reunion_r5.participants);\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"strong\");\n    i0.ɵɵtext(2, \"Lien Visio:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 32);\n    i0.ɵɵtext(4, \"Rejoindre la r\\u00E9union\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"href\", reunion_r5.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/reunions\", a1];\n};\nfunction ReunionListComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\")(3, \"h3\", 17)(4, \"a\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p\", 19);\n    i0.ɵɵpipe(7, \"highlightPresence\");\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 21);\n    i0.ɵɵelement(10, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 23)(17, \"strong\");\n    i0.ɵɵtext(18, \"Createur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, ReunionListComponent_div_7_div_1_div_20_Template, 5, 1, \"div\", 24);\n    i0.ɵɵelementStart(21, \"div\", 23)(22, \"strong\");\n    i0.ɵɵtext(23, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelement(25, \"br\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"date\");\n    i0.ɵɵelement(28, \"br\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, ReunionListComponent_div_7_div_1_div_31_Template, 5, 1, \"div\", 24);\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 21);\n    i0.ɵɵelement(35, \"path\", 27)(36, \"path\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_7_div_1_Template_a_click_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const reunion_r5 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.editReunion(reunion_r5._id));\n    });\n    i0.ɵɵtext(39, \" Modifier \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(30, _c0, reunion_r5.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(reunion_r5.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(7, 17, reunion_r5.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind2(12, 19, reunion_r5.date, \"medium\"), \" - \", reunion_r5.heureDebut, \" - \", reunion_r5.heureFin, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"px-2 py-1 text-xs rounded-full \" + ctx_r4.getStatutClass(reunion_r5.statut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 22, reunion_r5.statut), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", reunion_r5.createur.username, \" (\", reunion_r5.createur.email, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r5.participants.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r5.planning.titre, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Date de d\\u00E9but: \", i0.ɵɵpipeBind2(27, 24, reunion_r5.planning.dateDebut, \"mediumDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Date de fin: \", i0.ɵɵpipeBind2(30, 27, reunion_r5.planning.dateFin, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", reunion_r5.lienVisio);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r5.lieu || \"Lieu non sp\\u00E9cifi\\u00E9\", \" \");\n  }\n}\nfunction ReunionListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_7_div_1_Template, 40, 32, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.reunions);\n  }\n}\nexport class ReunionListComponent {\n  constructor(reunionService, router, authService) {\n    this.reunionService = reunionService;\n    this.router = router;\n    this.authService = authService;\n    this.reunions = [];\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadReunions();\n  }\n  loadReunions() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n    this.reunionService.getProchainesReunions(userId).subscribe({\n      next: reunions => {\n        console.log(reunions);\n        this.reunions = reunions.reunions;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = error;\n        this.loading = false;\n      }\n    });\n  }\n  getStatutClass(statut) {\n    switch (statut) {\n      case 'planifiee':\n        return 'bg-blue-100 text-blue-800';\n      case 'en_cours':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'terminee':\n        return 'bg-green-100 text-green-800';\n      case 'annulee':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  }\n  editReunion(id) {\n    console.log(id);\n    if (this.reunions) {\n      this.router.navigate(['/reunions/modifier', id]);\n    }\n  }\n  static {\n    this.ɵfac = function ReunionListComponent_Factory(t) {\n      return new (t || ReunionListComponent)(i0.ɵɵdirectiveInject(i1.ReunionService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionListComponent,\n      selectors: [[\"app-reunion-list\"]],\n      decls: 8,\n      vars: 4,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"space-y-4\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-12\", \"w-12\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-2\", \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"mt-1\", \"text-gray-500\"], [1, \"space-y-4\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"hover:shadow-lg\", \"transition-shadow\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"hover:text-purple-600\", 3, \"routerLink\"], [1, \"text-sm\", \"text-gray-600\", 3, \"innerHTML\"], [1, \"mt-2\", \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\"], [\"class\", \"mt-2 text-sm text-gray-600\", 4, \"ngIf\"], [1, \"mt-3\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"px-4\", \"py-2\", \"bg-blue-700\", \"text-white\", \"rounded-md\", \"transition-colors\", 3, \"click\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"]],\n      template: function ReunionListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Mes R\\u00E9unions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, ReunionListComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ReunionListComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ReunionListComponent_div_6_Template, 7, 0, \"div\", 3);\n          i0.ɵɵtemplate(7, ReunionListComponent_div_7_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i2.RouterLink, i4.TitleCasePipe, i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWxpc3QuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1saXN0L3JldW5pb24tbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "message", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵtextInterpolate2", "participant_r9", "username", "email", "ɵɵtemplate", "ReunionListComponent_div_7_div_1_div_20_li_4_Template", "ɵɵproperty", "reunion_r5", "participants", "ɵɵpropertyInterpolate", "lienVisio", "ɵɵsanitizeUrl", "ReunionListComponent_div_7_div_1_div_20_Template", "ReunionListComponent_div_7_div_1_div_31_Template", "ɵɵlistener", "ReunionListComponent_div_7_div_1_Template_a_click_38_listener", "restoredCtx", "ɵɵrestoreView", "_r13", "$implicit", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "editReunion", "_id", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "titre", "ɵɵpipeBind1", "description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate3", "ɵɵpipeBind2", "date", "heureDebut", "heure<PERSON>in", "ɵɵclassMap", "ctx_r4", "getStatutClass", "statut", "<PERSON>ur", "length", "planning", "dateDebut", "dateFin", "lieu", "ReunionListComponent_div_7_div_1_Template", "ctx_r3", "reunions", "ReunionListComponent", "constructor", "reunionService", "router", "authService", "loading", "ngOnInit", "loadReunions", "userId", "getCurrentUserId", "getProchainesReunions", "subscribe", "next", "console", "log", "navigate", "ɵɵdirectiveInject", "i1", "ReunionService", "i2", "Router", "i3", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ReunionListComponent_Template", "rf", "ctx", "ReunionListComponent_div_4_Template", "ReunionListComponent_div_5_Template", "ReunionListComponent_div_6_Template", "ReunionListComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-list\\reunion-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-list\\reunion-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ReunionService }  from 'src/app/services/reunion.service';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport {Router} from \"@angular/router\";\n\n@Component({\n  selector: 'app-reunion-list',\n  templateUrl: './reunion-list.component.html',\n  styleUrls: ['./reunion-list.component.css']\n})\nexport class ReunionListComponent implements OnInit {\n  reunions: any[] = [];\n  loading = true;\n  error: any;\n\n  constructor(\n    private reunionService: ReunionService,\n    private router:Router,\n    private authService: AuthuserService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunions();\n  }\n\n  loadReunions(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    this.reunionService.getProchainesReunions(userId).subscribe({\n      next: (reunions:any) => {\n        console.log(reunions)\n        this.reunions = reunions.reunions;\n        this.loading = false;\n      },\n      error: (error:any) => {\n        this.error = error;\n        this.loading = false;\n      }\n    });\n  }\n\n  getStatutClass(statut: string): string {\n    switch (statut) {\n      case 'planifiee': return 'bg-blue-100 text-blue-800';\n      case 'en_cours': return 'bg-yellow-100 text-yellow-800';\n      case 'terminee': return 'bg-green-100 text-green-800';\n      case 'annulee': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  }\n\n  editReunion(id:string) {\n    console.log(id)\n      if (this.reunions) {\n      this.router.navigate(['/reunions/modifier', id]);\n\n  }\n  }\n}", "<div class=\"container mx-auto px-4 py-6\">\n  <div class=\"flex justify-between items-center mb-6\">\n    <h1 class=\"text-2xl font-bold text-gray-800\">Mes Réunions</h1>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    Erreur lors du chargement des réunions: {{ error.message }}\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length === 0\" class=\"text-center py-8\">\n    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n    </svg>\n    <h3 class=\"mt-2 text-lg font-medium text-gray-900\">Aucune réunion prévue</h3>\n    <p class=\"mt-1 text-gray-500\">Commencez par planifier une nouvelle réunion.</p>\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length > 0\" class=\"space-y-4\">\n    <div *ngFor=\"let reunion of reunions\"\n         class=\"bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow\">\n      <div class=\"flex justify-between items-start\">\n        <div>\n          <h3 class=\"text-lg font-semibold text-gray-800\">\n            <a [routerLink]=\"['/reunions', reunion.id]\" class=\"hover:text-purple-600\">{{ reunion.titre }}</a>\n          </h3>\n          <p class=\"text-sm text-gray-600\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n          <div class=\"mt-2 flex items-center text-sm text-gray-500\">\n            <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            {{ reunion.date | date:'medium' }} - {{ reunion.heureDebut }} - {{ reunion.heureFin }}\n          </div>\n        </div>\n        <span [class]=\"'px-2 py-1 text-xs rounded-full ' + getStatutClass(reunion.statut)\">\n          {{ reunion.statut | titlecase }}\n        </span>\n      </div>\n\n      <!-- Creator Info -->\n      <div class=\"mt-2 text-sm text-gray-600\">\n        <strong>Createur:</strong> {{ reunion.createur.username }} ({{ reunion.createur.email }})\n      </div>\n\n      <!-- Participants -->\n      <div *ngIf=\"reunion.participants.length > 0\" class=\"mt-2 text-sm text-gray-600\">\n        <strong>Participants:</strong>\n        <ul class=\"list-disc pl-5\">\n          <li *ngFor=\"let participant of reunion.participants\">{{ participant.username }} ({{ participant.email }})</li>\n        </ul>\n      </div>\n\n      <!-- Planning Info -->\n      <div class=\"mt-2 text-sm text-gray-600\">\n        <strong>Planning:</strong> {{ reunion.planning.titre }}<br />\n        Date de début: {{ reunion.planning.dateDebut | date:'mediumDate' }}<br />\n        Date de fin: {{ reunion.planning.dateFin | date:'mediumDate' }}\n      </div>\n\n      <!-- Lien Visio -->\n      <div *ngIf=\"reunion.lienVisio\" class=\"mt-2 text-sm text-gray-600\">\n        <strong>Lien Visio:</strong> <a href=\"{{ reunion.lienVisio }}\" class=\"text-blue-600 hover:underline\" target=\"_blank\">Rejoindre la réunion</a>\n      </div>\n\n      <div class=\"mt-3 pt-3 border-t border-gray-100 flex justify-between items-center\">\n        <div class=\"flex items-center text-sm text-gray-500\">\n          <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n          </svg>\n          {{ reunion.lieu || 'Lieu non spécifié' }}\n        </div>\n        <a (click)=\"editReunion(reunion._id)\"\n           class=\"px-4 py-2 bg-blue-700 text-white rounded-md  transition-colors\">\n          Modifier\n        </a>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": ";;;;;;;ICKEA,EAAA,CAAAC,cAAA,aAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA4F;IAC9FF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,mDAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,MACF;;;;;IAEAT,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAU,cAAA,EAAmG;IAAnGV,EAAA,CAAAC,cAAA,aAAmG;IACjGD,EAAA,CAAAE,SAAA,eAAmK;IACrKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,eAAA,EAAmD;IAAnDX,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAI,MAAA,sCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAI,MAAA,yDAA6C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAiCzEH,EAAA,CAAAC,cAAA,SAAqD;IAAAD,EAAA,CAAAI,MAAA,GAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAAzDH,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAY,kBAAA,KAAAC,cAAA,CAAAC,QAAA,QAAAD,cAAA,CAAAE,KAAA,MAAoD;;;;;IAH7Gf,EAAA,CAAAC,cAAA,cAAgF;IACtED,EAAA,CAAAI,MAAA,oBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAgB,UAAA,IAAAC,qDAAA,iBAA8G;IAChHjB,EAAA,CAAAG,YAAA,EAAK;;;;IADyBH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkB,UAAA,YAAAC,UAAA,CAAAC,YAAA,CAAuB;;;;;IAYvDpB,EAAA,CAAAC,cAAA,cAAkE;IACxDD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,cAAA,YAAwF;IAAAD,EAAA,CAAAI,MAAA,gCAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAA7GH,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAqB,qBAAA,SAAAF,UAAA,CAAAG,SAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAA8B;;;;;;;;;IA1ClEvB,EAAA,CAAAC,cAAA,cACiF;IAICD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEnGH,EAAA,CAAAE,SAAA,YAA2F;;IAC3FF,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAU,cAAA,EAAgF;IAAhFV,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,gBAAwH;IAC1HF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAW,eAAA,EAAmF;IAAnFX,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAAwC;IAC9BD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,IAC7B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgB,UAAA,KAAAQ,gDAAA,kBAKM;IAGNxB,EAAA,CAAAC,cAAA,eAAwC;IAC9BD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAE,SAAA,UAAM;IAC7DF,EAAA,CAAAI,MAAA,IAAmE;;IAAAJ,EAAA,CAAAE,SAAA,UAAM;IACzEF,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgB,UAAA,KAAAS,gDAAA,kBAEM;IAENzB,EAAA,CAAAC,cAAA,eAAkF;IAE9ED,EAAA,CAAAU,cAAA,EAAgF;IAAhFV,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAE,SAAA,gBAA+J;IAEjKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,eAAA,EAC0E;IAD1EX,EAAA,CAAAC,cAAA,aAC0E;IADvED,EAAA,CAAA0B,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,WAAA,GAAA5B,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAX,UAAA,GAAAS,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAhB,UAAA,CAAAiB,GAAA,CAAwB;IAAA,EAAC;IAEnCpC,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAnDGH,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAAqC,eAAA,KAAAC,GAAA,EAAAnB,UAAA,CAAAoB,EAAA,EAAwC;IAA+BvC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAwC,iBAAA,CAAArB,UAAA,CAAAsB,KAAA,CAAmB;IAE9DzC,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAkB,UAAA,cAAAlB,EAAA,CAAA0C,WAAA,QAAAvB,UAAA,CAAAwB,WAAA,GAAA3C,EAAA,CAAA4C,cAAA,CAAqD;IAKpF5C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAA6C,kBAAA,MAAA7C,EAAA,CAAA8C,WAAA,SAAA3B,UAAA,CAAA4B,IAAA,oBAAA5B,UAAA,CAAA6B,UAAA,SAAA7B,UAAA,CAAA8B,QAAA,MACF;IAEIjD,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAkD,UAAA,qCAAAC,MAAA,CAAAC,cAAA,CAAAjC,UAAA,CAAAkC,MAAA,EAA4E;IAChFrD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA0C,WAAA,SAAAvB,UAAA,CAAAkC,MAAA,OACF;IAK2BrD,EAAA,CAAAK,SAAA,GAC7B;IAD6BL,EAAA,CAAAY,kBAAA,MAAAO,UAAA,CAAAmC,QAAA,CAAAxC,QAAA,QAAAK,UAAA,CAAAmC,QAAA,CAAAvC,KAAA,OAC7B;IAGMf,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAkB,UAAA,SAAAC,UAAA,CAAAC,YAAA,CAAAmC,MAAA,KAAqC;IASdvD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,kBAAA,MAAAa,UAAA,CAAAqC,QAAA,CAAAf,KAAA,KAA4B;IACvDzC,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAAM,kBAAA,0BAAAN,EAAA,CAAA8C,WAAA,SAAA3B,UAAA,CAAAqC,QAAA,CAAAC,SAAA,oBAAmE;IACnEzD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,mBAAAN,EAAA,CAAA8C,WAAA,SAAA3B,UAAA,CAAAqC,QAAA,CAAAE,OAAA,qBACF;IAGM1D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkB,UAAA,SAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUzBtB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAa,UAAA,CAAAwC,IAAA,uCACF;;;;;IArDN3D,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAgB,UAAA,IAAA4C,yCAAA,oBA0DM;IACR5D,EAAA,CAAAG,YAAA,EAAM;;;;IA3DqBH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAkB,UAAA,YAAA2C,MAAA,CAAAC,QAAA,CAAW;;;ADXxC,OAAM,MAAOC,oBAAoB;EAK/BC,YACUC,cAA8B,EAC9BC,MAAa,EACbC,WAA4B;IAF5B,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAPrB,KAAAL,QAAQ,GAAU,EAAE;IACpB,KAAAM,OAAO,GAAG,IAAI;EAOX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,MAAMC,MAAM,GAAG,IAAI,CAACJ,WAAW,CAACK,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb,IAAI,CAACN,cAAc,CAACQ,qBAAqB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAC;MAC1DC,IAAI,EAAGb,QAAY,IAAI;QACrBc,OAAO,CAACC,GAAG,CAACf,QAAQ,CAAC;QACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;QACjC,IAAI,CAACM,OAAO,GAAG,KAAK;MACtB,CAAC;MACD5D,KAAK,EAAGA,KAAS,IAAI;QACnB,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC4D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAhB,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD,KAAK,UAAU;QAAE,OAAO,+BAA+B;MACvD,KAAK,UAAU;QAAE,OAAO,6BAA6B;MACrD,KAAK,SAAS;QAAE,OAAO,yBAAyB;MAChD;QAAS,OAAO,2BAA2B;;EAE/C;EAEAlB,WAAWA,CAACI,EAAS;IACnBqC,OAAO,CAACC,GAAG,CAACtC,EAAE,CAAC;IACb,IAAI,IAAI,CAACuB,QAAQ,EAAE;MACnB,IAAI,CAACI,MAAM,CAACY,QAAQ,CAAC,CAAC,oBAAoB,EAAEvC,EAAE,CAAC,CAAC;;EAGpD;;;uBAhDWwB,oBAAoB,EAAA/D,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnF,EAAA,CAAA+E,iBAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApBtB,oBAAoB;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXjC5F,EAAA,CAAAC,cAAA,aAAyC;UAEQD,EAAA,CAAAI,MAAA,wBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAGhEH,EAAA,CAAAgB,UAAA,IAAA8E,mCAAA,iBAEM;UAEN9F,EAAA,CAAAgB,UAAA,IAAA+E,mCAAA,iBAEM;UAEN/F,EAAA,CAAAgB,UAAA,IAAAgF,mCAAA,iBAMM;UAENhG,EAAA,CAAAgB,UAAA,IAAAiF,mCAAA,iBA4DM;UACRjG,EAAA,CAAAG,YAAA,EAAM;;;UA7EEH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAkB,UAAA,SAAA2E,GAAA,CAAAzB,OAAA,CAAa;UAIbpE,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAkB,UAAA,SAAA2E,GAAA,CAAArF,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkB,UAAA,UAAA2E,GAAA,CAAAzB,OAAA,IAAAyB,GAAA,CAAA/B,QAAA,CAAAP,MAAA,OAAuC;UAQvCvD,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAkB,UAAA,UAAA2E,GAAA,CAAAzB,OAAA,IAAAyB,GAAA,CAAA/B,QAAA,CAAAP,MAAA,KAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}