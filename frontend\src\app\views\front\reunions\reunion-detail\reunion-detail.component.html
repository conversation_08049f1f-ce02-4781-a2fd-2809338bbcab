<div class="container mx-auto px-4 py-6">
  <!-- Bouton retour -->
  <button (click)="router.navigate(['/reunions'])"
          class="mb-4 flex items-center text-purple-600 hover:text-purple-800">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
    Retour aux réunions
  </button>

  <!-- Chargement -->
  <div *ngIf="loading" class="text-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
  </div>

  <!-- Erreur -->
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <!-- Détails de la réunion -->
  <div *ngIf="!loading && reunion" class="bg-white rounded-lg shadow-md p-6">
    <!-- Titre de la réunion -->
    <div class="flex justify-between items-start mb-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">{{ reunion.titre }}</h1>
        <p class="text-gray-600 mt-1" [innerHTML]="reunion.description | highlightPresence"></p>
      </div>
      <button (click)="editReunion()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
        Modifier
      </button>
    </div>

    <!-- Date et heure -->
    <div class="mb-6">
      <div class="flex items-center mb-2">
        <svg class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <span class="text-gray-700">
          {{ reunion.date | date:'fullDate' }}, {{ reunion.heureDebut }} - {{ reunion.heureFin }}
        </span>
      </div>
    </div>

    <!-- Créateur -->
    <div class="mb-4">
      <h2 class="text-lg font-semibold mb-2 text-gray-800">Créateur:</h2>
      <div class="flex items-center">
        <span class="text-gray-700">{{ reunion.createur?.username }} ({{ reunion.createur?.email }})</span>
      </div>
    </div>

    <!-- Participants -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-2 text-gray-800">Participants:</h2>
      <ul class="list-disc pl-5">
        <li *ngFor="let participant of reunion.participants" class="text-gray-700">
          {{ participant.username }} ({{ participant.email }})
        </li>
      </ul>
    </div>

    <!-- Planning -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-2 text-gray-800">Planning:</h2>
      <div class="text-gray-700">
        <p>{{ reunion.planning?.titre }}</p>
        <p>Du {{ reunion.planning?.dateDebut | date:'mediumDate' }} au {{ reunion.planning?.dateFin | date:'mediumDate' }}</p>
      </div>
    </div>

    <!-- Lieu -->
    <div *ngIf="reunion.lieu" class="mb-6">
      <h2 class="text-lg font-semibold mb-2 text-gray-800">Lieu:</h2>
      <div class="flex items-center">
        <svg class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <span class="text-gray-700">{{ reunion.lieu }}</span>
      </div>
    </div>

    <!-- Lien Visio -->
    <div *ngIf="reunion.lienVisio" class="mb-6">
      <h2 class="text-lg font-semibold mb-2 text-gray-800">Lien Visio:</h2>
      <a [href]="reunion.lienVisio" class="text-blue-600 hover:underline flex items-center" target="_blank">
        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        Rejoindre la réunion
      </a>
    </div>
  </div>
</div>