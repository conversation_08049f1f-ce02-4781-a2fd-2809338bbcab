{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"@angular/common\";\nfunction ReunionFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction ReunionFormComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_41_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nfunction ReunionFormComponent_div_41_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 33);\n    i0.ɵɵtext(2, \"Planning *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 34)(4, \"option\", 35);\n    i0.ɵɵtext(5, \"S\\u00E9lectionnez un planning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ReunionFormComponent_div_41_option_6_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ReunionFormComponent_div_41_div_7_Template, 2, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.plannings);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r6.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r6.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction ReunionFormComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtext(2, \"Planning s\\u00E9lectionn\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedPlanning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Du \", i0.ɵɵpipeBind2(7, 3, ctx_r7.selectedPlanning.dateDebut, \"mediumDate\"), \" au \", i0.ɵɵpipeBind2(8, 6, ctx_r7.selectedPlanning.dateFin, \"mediumDate\"), \" \");\n  }\n}\nfunction ReunionFormComponent_ng_container_47_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r14._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r14.username);\n  }\n}\nfunction ReunionFormComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_47_option_1_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r12 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r12);\n  }\n}\nexport class ReunionFormComponent {\n  constructor(fb, reunionService, planningService, userService, route, router, authService) {\n    this.fb = fb;\n    this.reunionService = reunionService;\n    this.planningService = planningService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.plannings = [];\n    this.loading = true;\n    this.isSubmitting = false;\n    this.error = null;\n    this.successMessage = null;\n    this.isEditMode = false;\n    this.currentReunionId = null;\n    this.planningIdFromUrl = null;\n    this.selectedPlanning = null;\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n    this.users$ = this.userService.getAllUsers();\n  }\n  ngOnInit() {\n    this.loadPlannings();\n    this.checkEditMode();\n    this.checkPlanningParam();\n  }\n  checkEditMode() {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n  loadPlannings() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: response => {\n        this.plannings = response.plannings || [];\n      },\n      error: err => {\n        this.error = err;\n      }\n    });\n  }\n  loadReunion(id) {\n    this.reunionService.getReunionById(id).subscribe({\n      next: reunion => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n  formatDateForInput(date) {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  checkPlanningParam() {\n    const planningId = this.route.snapshot.queryParamMap.get('planningId');\n    if (planningId) {\n      this.planningIdFromUrl = planningId;\n      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n      this.reunionForm.patchValue({\n        planning: planningId\n      });\n      // Récupérer les détails du planning pour l'affichage\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: response => {\n          this.selectedPlanning = response.planning;\n        },\n        error: err => {\n          console.error('Erreur lors de la récupération du planning:', err);\n        }\n      });\n    }\n  }\n  onSubmit() {\n    if (this.reunionForm.invalid) return;\n    this.isSubmitting = true;\n    this.error = null;\n    this.successMessage = null;\n    const formValue = this.reunionForm.value;\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n    const reunionData = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n    console.log(reunionData);\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: () => {\n        this.successMessage = 'Réunion créée avec succès!';\n        this.isSubmitting = false;\n        // Redirect to reunions list page\n        this.router.navigate(['/reunions']);\n      },\n      error: err => {\n        this.error = err;\n        this.isSubmitting = false;\n      }\n    });\n  }\n  resetForm() {\n    // Reset the form to its initial state\n    this.reunionForm.reset({\n      titre: '',\n      description: '',\n      date: '',\n      heureDebut: '',\n      heureFin: '',\n      lieu: '',\n      lienVisio: '',\n      planning: '',\n      participants: []\n    });\n    // Mark the form as pristine and untouched to reset validation states\n    this.reunionForm.markAsPristine();\n    this.reunionForm.markAsUntouched();\n  }\n  goReunion() {\n    this.router.navigate(['/reunions']);\n  }\n  static {\n    this.ɵfac = function ReunionFormComponent_Factory(t) {\n      return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionFormComponent,\n      selectors: [[\"app-reunion-form\"]],\n      decls: 54,\n      vars: 15,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [4, \"ngIf\"], [\"class\", \"bg-gray-50 p-3 rounded-md\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-green-100\", \"border\", \"border-green-400\", \"text-green-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"bg-gray-50\", \"p-3\", \"rounded-md\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-gray-900\", \"font-medium\"], [1, \"text-xs\", \"text-gray-500\", \"mt-1\"]],\n      template: function ReunionFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(4, ReunionFormComponent_div_4_Template, 2, 1, \"div\", 3);\n          i0.ɵɵtemplate(5, ReunionFormComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\")(8, \"label\", 6);\n          i0.ɵɵtext(9, \"Titre *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 7);\n          i0.ɵɵtemplate(11, ReunionFormComponent_div_11_Template, 2, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\")(13, \"label\", 9);\n          i0.ɵɵtext(14, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"textarea\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\")(18, \"label\", 12);\n          i0.ɵɵtext(19, \"Date *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 13);\n          i0.ɵɵtemplate(21, ReunionFormComponent_div_21_Template, 2, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\")(23, \"label\", 14);\n          i0.ɵɵtext(24, \"Heure de d\\u00E9but *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 15);\n          i0.ɵɵtemplate(26, ReunionFormComponent_div_26_Template, 2, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\")(28, \"label\", 16);\n          i0.ɵɵtext(29, \"Heure de fin *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 17);\n          i0.ɵɵtemplate(31, ReunionFormComponent_div_31_Template, 2, 0, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 18)(33, \"div\")(34, \"label\", 19);\n          i0.ɵɵtext(35, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\")(38, \"label\", 21);\n          i0.ɵɵtext(39, \"Lien Visio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(41, ReunionFormComponent_div_41_Template, 8, 2, \"div\", 23);\n          i0.ɵɵtemplate(42, ReunionFormComponent_div_42_Template, 9, 9, \"div\", 24);\n          i0.ɵɵelementStart(43, \"div\")(44, \"label\", 25);\n          i0.ɵɵtext(45, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 26);\n          i0.ɵɵtemplate(47, ReunionFormComponent_ng_container_47_Template, 2, 1, \"ng-container\", 23);\n          i0.ɵɵpipe(48, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 27)(50, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_50_listener() {\n            return ctx.goReunion();\n          });\n          i0.ɵɵtext(51, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 29);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", !ctx.planningIdFromUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.planningIdFromUrl && ctx.selectedPlanning);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(48, 13, ctx.users$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.AsyncPipe, i7.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWZvcm0uY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1mb3JtL3JldW5pb24tZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "message", "ctx_r1", "successMessage", "ɵɵproperty", "planning_r11", "id", "ɵɵtextInterpolate", "titre", "ɵɵtemplate", "ReunionFormComponent_div_41_option_6_Template", "ReunionFormComponent_div_41_div_7_Template", "ctx_r6", "plannings", "tmp_1_0", "reunionForm", "get", "invalid", "touched", "ctx_r7", "selectedPlanning", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "dateDebut", "dateFin", "user_r14", "_id", "username", "ɵɵelementContainerStart", "ReunionFormComponent_ng_container_47_option_1_Template", "ɵɵelementContainerEnd", "users_r12", "ReunionFormComponent", "constructor", "fb", "reunionService", "planningService", "userService", "route", "router", "authService", "loading", "isSubmitting", "isEditMode", "currentReunionId", "planningIdFromUrl", "group", "required", "description", "date", "heureDebut", "heure<PERSON>in", "lieu", "lienVisio", "planning", "participants", "users$", "getAllUsers", "ngOnInit", "loadPlannings", "checkEditMode", "checkPlanningParam", "reunionId", "snapshot", "paramMap", "loadReunion", "userId", "getCurrentUserId", "getPlanningsByUser", "subscribe", "next", "response", "err", "getReunionById", "reunion", "patchValue", "formatDateForInput", "planningId", "Date", "toISOString", "slice", "queryParamMap", "getPlanningById", "console", "onSubmit", "formValue", "value", "reunionData", "log", "createReunion", "navigate", "resetForm", "reset", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goReunion", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ReunionService", "i3", "PlanningService", "i4", "DataService", "i5", "ActivatedRoute", "Router", "i6", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ReunionFormComponent_Template", "rf", "ctx", "ɵɵlistener", "ReunionFormComponent_Template_form_ngSubmit_3_listener", "ReunionFormComponent_div_4_Template", "ReunionFormComponent_div_5_Template", "ɵɵelement", "ReunionFormComponent_div_11_Template", "ReunionFormComponent_div_21_Template", "ReunionFormComponent_div_26_Template", "ReunionFormComponent_div_31_Template", "ReunionFormComponent_div_41_Template", "ReunionFormComponent_div_42_Template", "ReunionFormComponent_ng_container_47_Template", "ReunionFormComponent_Template_button_click_50_listener", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-form\\reunion-form.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-form\\reunion-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ReunionService } from 'src/app/services/reunion.service';\nimport { PlanningService } from 'src/app/services/planning.service';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { Planning } from 'src/app/models/planning.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport {Observable} from \"rxjs\";\nimport {User} from \"@app/models/user.model\";\nimport {DataService} from \"@app/services/data.service\";\n\n@Component({\n  selector: 'app-reunion-form',\n  templateUrl: './reunion-form.component.html',\n  styleUrls: ['./reunion-form.component.css']\n})\nexport class ReunionFormComponent implements OnInit {\n  reunionForm: FormGroup;\n  plannings: Planning[] = [];\n  users$: Observable<User[]>;\n  loading = true;\n  isSubmitting = false;\n  error: any = null;\n  successMessage: string | null = null;\n  isEditMode = false;\n  currentReunionId: string | null = null;\n  planningIdFromUrl: string | null = null;\n  selectedPlanning: Planning | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private reunionService: ReunionService,\n    private planningService: PlanningService,\n    private userService: DataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private authService: AuthuserService\n  ) {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n\n\n    this.users$ = this.userService.getAllUsers();\n  }\n\n  ngOnInit(): void {\n    this.loadPlannings();\n    this.checkEditMode();\n    this.checkPlanningParam();\n  }\n\n  checkEditMode(): void {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n\n  loadPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: (response:any) => {\n        this.plannings = response.plannings || [];\n      },\n      error: (err) => {\n        this.error = err;\n      }\n    });\n  }\n\n  loadReunion(id: string): void {\n    this.reunionService.getReunionById(id).subscribe({\n      next: (reunion) => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: (err) => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n\n  formatDateForInput(date: Date | string): string {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  checkPlanningParam(): void {\n    const planningId = this.route.snapshot.queryParamMap.get('planningId');\n    if (planningId) {\n      this.planningIdFromUrl = planningId;\n\n      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n      this.reunionForm.patchValue({\n        planning: planningId\n      });\n\n      // Récupérer les détails du planning pour l'affichage\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: (response: any) => {\n          this.selectedPlanning = response.planning;\n        },\n        error: (err) => {\n          console.error('Erreur lors de la récupération du planning:', err);\n        }\n      });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.reunionForm.invalid) return;\n\n    this.isSubmitting = true;\n    this.error = null;\n    this.successMessage = null;\n    const formValue = this.reunionForm.value;\n\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n\n    const reunionData: any = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n\n    console.log(reunionData);\n\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: () => {\n        this.successMessage = 'Réunion créée avec succès!';\n        this.isSubmitting = false;\n        // Redirect to reunions list page\n        this.router.navigate(['/reunions']);\n      },\n      error: (err) => {\n        this.error = err;\n        this.isSubmitting = false;\n      }\n    });\n  }\n\n  resetForm(): void {\n    // Reset the form to its initial state\n    this.reunionForm.reset({\n      titre: '',\n      description: '',\n      date: '',\n      heureDebut: '',\n      heureFin: '',\n      lieu: '',\n      lienVisio: '',\n      planning: '',\n      participants: []\n    });\n\n    // Mark the form as pristine and untouched to reset validation states\n    this.reunionForm.markAsPristine();\n    this.reunionForm.markAsUntouched();\n  }\n\n\n  goReunion(): void {\n    this.router.navigate(['/reunions']);\n  }\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <h1 class=\"text-2xl font-bold text-gray-800 mb-6\">\n    {{ isEditMode ? 'Modifier la Réunion' : 'Nouvelle Réunion' }}\n  </h1>\n\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-lg shadow-md p-6\">\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n      {{ error.message || 'Une erreur est survenue' }}\n    </div>\n    <div *ngIf=\"successMessage\" class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n      {{ successMessage }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Titre -->\n      <div>\n        <label for=\"titre\" class=\"block text-sm font-medium text-gray-700\">Titre *</label>\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\n               class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\n             class=\"text-red-500 text-sm mt-1\">\n          Le titre est obligatoire\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div>\n        <label for=\"description\" class=\"block text-sm font-medium text-gray-700\">Description</label>\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\n                  class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\"></textarea>\n      </div>\n\n      <!-- Date and Time -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div>\n          <label for=\"date\" class=\"block text-sm font-medium text-gray-700\">Date *</label>\n          <input id=\"date\" type=\"date\" formControlName=\"date\"\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n          <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\n               class=\"text-red-500 text-sm mt-1\">\n            La date est obligatoire\n          </div>\n        </div>\n\n        <div>\n          <label for=\"heureDebut\" class=\"block text-sm font-medium text-gray-700\">Heure de début *</label>\n          <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n          <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\n               class=\"text-red-500 text-sm mt-1\">\n            L'heure de début est obligatoire\n          </div>\n        </div>\n\n        <div>\n          <label for=\"heureFin\" class=\"block text-sm font-medium text-gray-700\">Heure de fin *</label>\n          <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n          <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\n               class=\"text-red-500 text-sm mt-1\">\n            L'heure de fin est obligatoire\n          </div>\n        </div>\n      </div>\n\n      <!-- Lieu / Lien visio -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label for=\"lieu\" class=\"block text-sm font-medium text-gray-700\">Lieu</label>\n          <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n        </div>\n\n        <div>\n          <label for=\"lienVisio\" class=\"block text-sm font-medium text-gray-700\">Lien Visio</label>\n          <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n        </div>\n      </div>\n\n      <!-- Planning - affiché seulement si pas de planningId dans l'URL -->\n      <div *ngIf=\"!planningIdFromUrl\">\n        <label for=\"planning\" class=\"block text-sm font-medium text-gray-700\">Planning *</label>\n        <select id=\"planning\" formControlName=\"planning\"\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\n          <option value=\"\">Sélectionnez un planning</option>\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning.id\">{{ planning.titre }}</option>\n        </select>\n        <div *ngIf=\"reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched\"\n             class=\"text-red-500 text-sm mt-1\">\n          Le planning est obligatoire\n        </div>\n      </div>\n\n      <!-- Planning info - affiché quand planningId est dans l'URL -->\n      <div *ngIf=\"planningIdFromUrl && selectedPlanning\" class=\"bg-gray-50 p-3 rounded-md\">\n        <div class=\"text-sm font-medium text-gray-700\">Planning sélectionné:</div>\n        <div class=\"text-gray-900 font-medium\">{{ selectedPlanning.titre }}</div>\n        <div class=\"text-xs text-gray-500 mt-1\">\n          Du {{ selectedPlanning.dateDebut | date:'mediumDate' }} au {{ selectedPlanning.dateFin | date:'mediumDate' }}\n        </div>\n      </div>\n\n      <!-- Participants -->\n      <div>\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\n        <select formControlName=\"participants\" multiple\n                class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\">\n          <ng-container *ngIf=\"users$ | async as users\">\n            <option *ngFor=\"let user of users\" [value]=\"user._id\">{{ user.username }}</option>\n          </ng-container>\n        </select>\n      </div>\n    </div>\n\n    <div class=\"mt-6 flex justify-end space-x-3\">\n      <button type=\"button\" (click)=\"goReunion()\"\n              class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\">\n        Annuler\n      </button>\n      <button type=\"submit\" [disabled]=\"reunionForm.invalid || isSubmitting\"\n              class=\"px-4 py-2 rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50\">\n        {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer' }}\n      </button>\n    </div>\n  </form>\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICK/DC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,mCACF;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAC,cAAA,MACF;;;;;IAQIV,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBJH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBNH,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnDH,EAAA,CAAAW,UAAA,UAAAC,YAAA,CAAAC,EAAA,CAAqB;IAACb,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAc,iBAAA,CAAAF,YAAA,CAAAG,KAAA,CAAoB;;;;;IAEvFf,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,UAAgC;IACwCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxFH,EAAA,CAAAC,cAAA,iBACqH;IAClGD,EAAA,CAAAE,MAAA,oCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAgB,UAAA,IAAAC,6CAAA,qBAA8F;IAChGjB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAgB,UAAA,IAAAE,0CAAA,iBAGM;IACRlB,EAAA,CAAAG,YAAA,EAAM;;;;;IAN2BH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAW,UAAA,YAAAQ,MAAA,CAAAC,SAAA,CAAY;IAErCpB,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAW,UAAA,WAAAU,OAAA,GAAAF,MAAA,CAAAG,WAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,OAAA,OAAAH,OAAA,GAAAF,MAAA,CAAAG,WAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAI,OAAA,EAAkF;;;;;IAO1FzB,EAAA,CAAAC,cAAA,cAAqF;IACpCD,EAAA,CAAAE,MAAA,sCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1EH,EAAA,CAAAC,cAAA,cAAuC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHiCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAc,iBAAA,CAAAY,MAAA,CAAAC,gBAAA,CAAAZ,KAAA,CAA4B;IAEjEf,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA4B,kBAAA,SAAA5B,EAAA,CAAA6B,WAAA,OAAAH,MAAA,CAAAC,gBAAA,CAAAG,SAAA,yBAAA9B,EAAA,CAAA6B,WAAA,OAAAH,MAAA,CAAAC,gBAAA,CAAAI,OAAA,qBACF;;;;;IASI/B,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAW,UAAA,UAAAqB,QAAA,CAAAC,GAAA,CAAkB;IAACjC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAc,iBAAA,CAAAkB,QAAA,CAAAE,QAAA,CAAmB;;;;;IAD3ElC,EAAA,CAAAmC,uBAAA,GAA8C;IAC5CnC,EAAA,CAAAgB,UAAA,IAAAoB,sDAAA,qBAAkF;IACpFpC,EAAA,CAAAqC,qBAAA,EAAe;;;;IADYrC,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAW,UAAA,YAAA2B,SAAA,CAAQ;;;AD5F7C,OAAM,MAAOC,oBAAoB;EAa/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,WAA4B;IAN5B,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAlBrB,KAAA3B,SAAS,GAAe,EAAE;IAE1B,KAAA4B,OAAO,GAAG,IAAI;IACd,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAA1C,KAAK,GAAQ,IAAI;IACjB,KAAAG,cAAc,GAAkB,IAAI;IACpC,KAAAwC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,iBAAiB,GAAkB,IAAI;IACvC,KAAAzB,gBAAgB,GAAoB,IAAI;IAWtC,IAAI,CAACL,WAAW,GAAG,IAAI,CAACmB,EAAE,CAACY,KAAK,CAAC;MAC/BtC,KAAK,EAAE,CAAC,EAAE,EAAEhB,UAAU,CAACuD,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,EAAEzD,UAAU,CAACuD,QAAQ,CAAC;MAC/BG,UAAU,EAAE,CAAC,EAAE,EAAE1D,UAAU,CAACuD,QAAQ,CAAC;MACrCI,QAAQ,EAAE,CAAC,EAAE,EAAE3D,UAAU,CAACuD,QAAQ,CAAC;MACnCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAE9D,UAAU,CAACuD,QAAQ,CAAC;MACnCQ,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IAGF,IAAI,CAACC,MAAM,GAAG,IAAI,CAACnB,WAAW,CAACoB,WAAW,EAAE;EAC9C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAD,aAAaA,CAAA;IACX,MAAME,SAAS,GAAG,IAAI,CAACxB,KAAK,CAACyB,QAAQ,CAACC,QAAQ,CAAChD,GAAG,CAAC,IAAI,CAAC;IACxD,IAAI8C,SAAS,EAAE;MACb,IAAI,CAACnB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAGkB,SAAS;MACjC,IAAI,CAACG,WAAW,CAACH,SAAS,CAAC;;EAE/B;EAEAH,aAAaA,CAAA;IACX,MAAMO,MAAM,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb,IAAI,CAAC9B,eAAe,CAACgC,kBAAkB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAY,IAAI;QACrB,IAAI,CAAC1D,SAAS,GAAG0D,QAAQ,CAAC1D,SAAS,IAAI,EAAE;MAC3C,CAAC;MACDb,KAAK,EAAGwE,GAAG,IAAI;QACb,IAAI,CAACxE,KAAK,GAAGwE,GAAG;MAClB;KACD,CAAC;EACJ;EAEAP,WAAWA,CAAC3D,EAAU;IACpB,IAAI,CAAC6B,cAAc,CAACsC,cAAc,CAACnE,EAAE,CAAC,CAAC+D,SAAS,CAAC;MAC/CC,IAAI,EAAGI,OAAO,IAAI;QAChB,IAAI,CAAC3D,WAAW,CAAC4D,UAAU,CAAC;UAC1BnE,KAAK,EAAEkE,OAAO,CAAClE,KAAK;UACpBwC,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;UAChCzB,SAAS,EAAE,IAAI,CAACqD,kBAAkB,CAACF,OAAO,CAACnD,SAAS,CAAC;UACrDC,OAAO,EAAE,IAAI,CAACoD,kBAAkB,CAACF,OAAO,CAAClD,OAAO,CAAC;UACjD4B,IAAI,EAAEsB,OAAO,CAACtB,IAAI;UAClBC,SAAS,EAAEqB,OAAO,CAACrB,SAAS;UAC5BwB,UAAU,EAAEH,OAAO,CAACG,UAAU;UAC9BtB,YAAY,EAAEmB,OAAO,CAACnB;SACvB,CAAC;QACF,IAAI,CAACd,OAAO,GAAG,KAAK;MACtB,CAAC;MACDzC,KAAK,EAAGwE,GAAG,IAAI;QACb,IAAI,CAACxE,KAAK,GAAGwE,GAAG;QAChB,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmC,kBAAkBA,CAAC3B,IAAmB;IACpC,OAAO,IAAI6B,IAAI,CAAC7B,IAAI,CAAC,CAAC8B,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACpD;;EAEAnB,kBAAkBA,CAAA;IAChB,MAAMgB,UAAU,GAAG,IAAI,CAACvC,KAAK,CAACyB,QAAQ,CAACkB,aAAa,CAACjE,GAAG,CAAC,YAAY,CAAC;IACtE,IAAI6D,UAAU,EAAE;MACd,IAAI,CAAChC,iBAAiB,GAAGgC,UAAU;MAEnC;MACA,IAAI,CAAC9D,WAAW,CAAC4D,UAAU,CAAC;QAC1BrB,QAAQ,EAAEuB;OACX,CAAC;MAEF;MACA,IAAI,CAACzC,eAAe,CAAC8C,eAAe,CAACL,UAAU,CAAC,CAACR,SAAS,CAAC;QACzDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACnD,gBAAgB,GAAGmD,QAAQ,CAACjB,QAAQ;QAC3C,CAAC;QACDtD,KAAK,EAAGwE,GAAG,IAAI;UACbW,OAAO,CAACnF,KAAK,CAAC,6CAA6C,EAAEwE,GAAG,CAAC;QACnE;OACD,CAAC;;EAEN;EAEAY,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrE,WAAW,CAACE,OAAO,EAAE;IAE9B,IAAI,CAACyB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC1C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACG,cAAc,GAAG,IAAI;IAC1B,MAAMkF,SAAS,GAAG,IAAI,CAACtE,WAAW,CAACuE,KAAK;IAExC,MAAMrC,IAAI,GAAGoC,SAAS,CAACpC,IAAI,CAAC,CAAC;IAC7B,MAAMC,UAAU,GAAGmC,SAAS,CAACnC,UAAU,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAGkC,SAAS,CAAClC,QAAQ;IAEnC,MAAMoC,WAAW,GAAQ;MACvB/E,KAAK,EAAE6E,SAAS,CAAC7E,KAAK;MACtBwC,WAAW,EAAEqC,SAAS,CAACrC,WAAW;MAClCC,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA,QAAQ;MAClBC,IAAI,EAAEiC,SAAS,CAACjC,IAAI;MACpBC,SAAS,EAAEgC,SAAS,CAAChC,SAAS;MAC9BC,QAAQ,EAAE+B,SAAS,CAAC/B,QAAQ;MAC5BC,YAAY,EAAE8B,SAAS,CAAC9B,YAAY,IAAI;KACzC;IAED4B,OAAO,CAACK,GAAG,CAACD,WAAW,CAAC;IAExB,IAAI,CAACpD,cAAc,CAACsD,aAAa,CAACF,WAAW,CAAC,CAAClB,SAAS,CAAC;MACvDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnE,cAAc,GAAG,4BAA4B;QAClD,IAAI,CAACuC,YAAY,GAAG,KAAK;QACzB;QACA,IAAI,CAACH,MAAM,CAACmD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACD1F,KAAK,EAAGwE,GAAG,IAAI;QACb,IAAI,CAACxE,KAAK,GAAGwE,GAAG;QAChB,IAAI,CAAC9B,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEAiD,SAASA,CAAA;IACP;IACA,IAAI,CAAC5E,WAAW,CAAC6E,KAAK,CAAC;MACrBpF,KAAK,EAAE,EAAE;MACTwC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACxC,WAAW,CAAC8E,cAAc,EAAE;IACjC,IAAI,CAAC9E,WAAW,CAAC+E,eAAe,EAAE;EACpC;EAGAC,SAASA,CAAA;IACP,IAAI,CAACxD,MAAM,CAACmD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;;;uBAjLW1D,oBAAoB,EAAAvC,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA7G,EAAA,CAAAuG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAAuG,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAjH,EAAA,CAAAuG,iBAAA,CAAAS,EAAA,CAAAE,MAAA,GAAAlH,EAAA,CAAAuG,iBAAA,CAAAY,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApB7E,oBAAoB;MAAA8E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjC3H,EAAA,CAAAC,cAAA,aAAmD;UAE/CD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,cAAkG;UAAlED,EAAA,CAAA6H,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UACrD3F,EAAA,CAAAgB,UAAA,IAAA+G,mCAAA,iBAEM;UACN/H,EAAA,CAAAgB,UAAA,IAAAgH,mCAAA,iBAEM;UAENhI,EAAA,CAAAC,cAAA,aAAoC;UAGmCD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClFH,EAAA,CAAAiI,SAAA,gBACoH;UACpHjI,EAAA,CAAAgB,UAAA,KAAAkH,oCAAA,iBAGM;UACRlI,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UACsED,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAAiI,SAAA,oBACkI;UACpIjI,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmD;UAEmBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChFH,EAAA,CAAAiI,SAAA,iBACoH;UACpHjI,EAAA,CAAAgB,UAAA,KAAAmH,oCAAA,iBAGM;UACRnI,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACqED,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChGH,EAAA,CAAAiI,SAAA,iBACoH;UACpHjI,EAAA,CAAAgB,UAAA,KAAAoH,oCAAA,iBAGM;UACRpI,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAAiI,SAAA,iBACoH;UACpHjI,EAAA,CAAAgB,UAAA,KAAAqH,oCAAA,iBAGM;UACRrI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAmD;UAEmBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAiI,SAAA,iBACoH;UACtHjI,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACoED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzFH,EAAA,CAAAiI,SAAA,iBACoH;UACtHjI,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAgB,UAAA,KAAAsH,oCAAA,kBAWM;UAGNtI,EAAA,CAAAgB,UAAA,KAAAuH,oCAAA,kBAMM;UAGNvI,EAAA,CAAAC,cAAA,WAAK;UACoDD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBACiI;UAC/HD,EAAA,CAAAgB,UAAA,KAAAwH,6CAAA,2BAEe;;UACjBxI,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6C;UACrBD,EAAA,CAAA6H,UAAA,mBAAAY,uDAAA;YAAA,OAASb,GAAA,CAAAtB,SAAA,EAAW;UAAA,EAAC;UAEzCtG,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAC0H;UACxHD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;UAzHXH,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAuH,GAAA,CAAA1E,UAAA,6DACF;UAEMlD,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAW,UAAA,cAAAiH,GAAA,CAAAtG,WAAA,CAAyB;UACvBtB,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAW,UAAA,SAAAiH,GAAA,CAAArH,KAAA,CAAW;UAGXP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAW,UAAA,SAAAiH,GAAA,CAAAlH,cAAA,CAAoB;UAUhBV,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAW,UAAA,WAAA+H,OAAA,GAAAd,GAAA,CAAAtG,WAAA,CAAAC,GAAA,4BAAAmH,OAAA,CAAAlH,OAAA,OAAAkH,OAAA,GAAAd,GAAA,CAAAtG,WAAA,CAAAC,GAAA,4BAAAmH,OAAA,CAAAjH,OAAA,EAA4E;UAmB1EzB,EAAA,CAAAI,SAAA,IAA0E;UAA1EJ,EAAA,CAAAW,UAAA,WAAAgI,OAAA,GAAAf,GAAA,CAAAtG,WAAA,CAAAC,GAAA,2BAAAoH,OAAA,CAAAnH,OAAA,OAAAmH,OAAA,GAAAf,GAAA,CAAAtG,WAAA,CAAAC,GAAA,2BAAAoH,OAAA,CAAAlH,OAAA,EAA0E;UAU1EzB,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAW,UAAA,WAAAiI,OAAA,GAAAhB,GAAA,CAAAtG,WAAA,CAAAC,GAAA,iCAAAqH,OAAA,CAAApH,OAAA,OAAAoH,OAAA,GAAAhB,GAAA,CAAAtG,WAAA,CAAAC,GAAA,iCAAAqH,OAAA,CAAAnH,OAAA,EAAsF;UAUtFzB,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAW,UAAA,WAAAkI,OAAA,GAAAjB,GAAA,CAAAtG,WAAA,CAAAC,GAAA,+BAAAsH,OAAA,CAAArH,OAAA,OAAAqH,OAAA,GAAAjB,GAAA,CAAAtG,WAAA,CAAAC,GAAA,+BAAAsH,OAAA,CAAApH,OAAA,EAAkF;UAuBtFzB,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAW,UAAA,UAAAiH,GAAA,CAAAxE,iBAAA,CAAwB;UAcxBpD,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAW,UAAA,SAAAiH,GAAA,CAAAxE,iBAAA,IAAAwE,GAAA,CAAAjG,gBAAA,CAA2C;UAa9B3B,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAW,UAAA,SAAAX,EAAA,CAAA8I,WAAA,SAAAlB,GAAA,CAAA7D,MAAA,EAAqB;UAYlB/D,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAW,UAAA,aAAAiH,GAAA,CAAAtG,WAAA,CAAAE,OAAA,IAAAoG,GAAA,CAAA3E,YAAA,CAAgD;UAEpEjD,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAuH,GAAA,CAAA3E,YAAA,4CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}