{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../services/reunion.service\";\nimport * as i3 from \"@angular/common\";\nfunction ReunionDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ReunionDetailComponent_div_7_li_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", participant_r6.username, \" (\", participant_r6.email, \") \");\n  }\n}\nfunction ReunionDetailComponent_div_7_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h2\", 21);\n    i0.ɵɵtext(2, \"Lieu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 17);\n    i0.ɵɵelement(5, \"path\", 26)(6, \"path\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"span\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.reunion.lieu);\n  }\n}\nfunction ReunionDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h2\", 21);\n    i0.ɵɵtext(2, \"Lien Visio:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 29);\n    i0.ɵɵelement(5, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Rejoindre la r\\u00E9union \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r5.reunion.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ReunionDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\")(3, \"h1\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p\", 13);\n    i0.ɵɵpipe(6, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_7_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.editReunion());\n    });\n    i0.ɵɵtext(8, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 17);\n    i0.ɵɵelement(12, \"path\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\", 19);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"h2\", 21);\n    i0.ɵɵtext(18, \"Cr\\u00E9ateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 22)(20, \"span\", 19);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"h2\", 21);\n    i0.ɵɵtext(24, \"Participants:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ul\", 23);\n    i0.ɵɵtemplate(26, ReunionDetailComponent_div_7_li_26_Template, 2, 2, \"li\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 15)(28, \"h2\", 21);\n    i0.ɵɵtext(29, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 19)(31, \"p\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\");\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"date\");\n    i0.ɵɵpipe(36, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, ReunionDetailComponent_div_7_div_37_Template, 9, 1, \"div\", 25);\n    i0.ɵɵtemplate(38, ReunionDetailComponent_div_7_div_38_Template, 7, 1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(6, 13, ctx_r2.reunion.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind2(15, 15, ctx_r2.reunion.date, \"fullDate\"), \", \", ctx_r2.reunion.heureDebut, \" - \", ctx_r2.reunion.heureFin, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.username, \" (\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.email, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.reunion.participants);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Du \", i0.ɵɵpipeBind2(35, 18, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateDebut, \"mediumDate\"), \" au \", i0.ɵɵpipeBind2(36, 21, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateFin, \"mediumDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lieu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lienVisio);\n  }\n}\nexport class ReunionDetailComponent {\n  constructor(route, reunionService) {\n    this.route = route;\n    this.reunionService = reunionService;\n    this.reunion = null;\n    this.loading = true;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadReunion();\n  }\n  loadReunion() {\n    this.loading = true;\n    this.error = null;\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion manquant';\n      this.loading = false;\n      return;\n    }\n    // Simulation de chargement (à remplacer par un appel au service)\n    setTimeout(() => {\n      // Données fictives pour la démonstration\n      this.reunion = {\n        _id: id,\n        titre: 'Réunion de projet',\n        description: \"Discussion sur l'avancement du projet et les prochaines étapes\",\n        dateDebut: new Date('2023-06-15T10:00:00'),\n        dateFin: new Date('2023-06-15T11:30:00'),\n        lieu: 'Salle de conférence A',\n        lienVisio: 'https://meet.google.com/abc-defg-hij',\n        planningId: {\n          _id: 'planning123',\n          titre: 'Planning du projet X'\n        },\n        participants: [{\n          _id: 'user1',\n          username: 'Jean Dupont',\n          image: 'assets/images/default-avatar.png'\n        }, {\n          _id: 'user2',\n          username: 'Marie Martin',\n          image: 'assets/images/default-avatar.png'\n        }]\n      };\n      this.loading = false;\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function ReunionDetailComponent_Factory(t) {\n      return new (t || ReunionDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ReunionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionDetailComponent,\n      selectors: [[\"app-reunion-detail\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-gray-600\", \"mt-1\", 3, \"innerHTML\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"mb-2\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\", \"text-gray-800\"], [1, \"flex\", \"items-center\"], [1, \"list-disc\", \"pl-5\"], [\"class\", \"text-gray-700\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", \"flex\", \"items-center\", 3, \"href\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n      template: function ReunionDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function ReunionDetailComponent_Template_button_click_1_listener() {\n            return ctx.router.navigate([\"/reunions\"]);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(2, \"svg\", 2);\n          i0.ɵɵelement(3, \"path\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Retour aux r\\u00E9unions \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, ReunionDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n          i0.ɵɵtemplate(6, ReunionDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, ReunionDetailComponent_div_7_Template, 39, 24, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunion);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWRldGFpbC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1kZXRhaWwvcmV1bmlvbi1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵtextInterpolate2", "participant_r6", "username", "email", "ɵɵnamespaceSVG", "ɵɵtextInterpolate", "ctx_r4", "reunion", "lieu", "ɵɵproperty", "ctx_r5", "lienVisio", "ɵɵsanitizeUrl", "ɵɵlistener", "ReunionDetailComponent_div_7_Template_button_click_7_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "editReunion", "ɵɵtemplate", "ReunionDetailComponent_div_7_li_26_Template", "ReunionDetailComponent_div_7_div_37_Template", "ReunionDetailComponent_div_7_div_38_Template", "ctx_r2", "titre", "ɵɵpipeBind1", "description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate3", "ɵɵpipeBind2", "date", "heureDebut", "heure<PERSON>in", "<PERSON>ur", "participants", "planning", "dateDebut", "dateFin", "ReunionDetailComponent", "constructor", "route", "reunionService", "loading", "ngOnInit", "loadReunion", "id", "snapshot", "paramMap", "get", "setTimeout", "_id", "Date", "planningId", "image", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ReunionService", "selectors", "decls", "vars", "consts", "template", "ReunionDetailComponent_Template", "rf", "ctx", "ReunionDetailComponent_Template_button_click_1_listener", "router", "navigate", "ReunionDetailComponent_div_5_Template", "ReunionDetailComponent_div_6_Template", "ReunionDetailComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { ReunionService } from '../../../../services/reunion.service';\n\ninterface Participant {\n  _id: string;\n  username: string;\n  image?: string;\n}\n\ninterface Planning {\n  _id: string;\n  titre: string;\n}\n\ninterface Reunion {\n  _id: string;\n  titre: string;\n  description: string;\n  dateDebut: Date;\n  dateFin: Date;\n  lieu?: string;\n  lienVisio?: string;\n  planningId?: Planning;\n  participants?: Participant[];\n}\n\n@Component({\n  selector: 'app-reunion-detail',\n  templateUrl: './reunion-detail.component.html',\n  styleUrls: ['./reunion-detail.component.css'],\n})\nexport class ReunionDetailComponent implements OnInit {\n  reunion: Reunion | null = null;\n  loading = true;\n  error: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private reunionService: ReunionService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunion();\n  }\n\n  loadReunion(): void {\n    this.loading = true;\n    this.error = null;\n\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion manquant';\n      this.loading = false;\n      return;\n    }\n\n    // Simulation de chargement (à remplacer par un appel au service)\n    setTimeout(() => {\n      // Données fictives pour la démonstration\n      this.reunion = {\n        _id: id,\n        titre: 'Réunion de projet',\n        description:\n          \"Discussion sur l'avancement du projet et les prochaines étapes\",\n        dateDebut: new Date('2023-06-15T10:00:00'),\n        dateFin: new Date('2023-06-15T11:30:00'),\n        lieu: 'Salle de conférence A',\n        lienVisio: 'https://meet.google.com/abc-defg-hij',\n        planningId: {\n          _id: 'planning123',\n          titre: 'Planning du projet X',\n        },\n        participants: [\n          {\n            _id: 'user1',\n            username: 'Jean Dupont',\n            image: 'assets/images/default-avatar.png',\n          },\n          {\n            _id: 'user2',\n            username: 'Marie Martin',\n            image: 'assets/images/default-avatar.png',\n          },\n        ],\n      };\n\n      this.loading = false;\n    }, 1000);\n  }\n}\n", "<div class=\"container mx-auto px-4 py-6\">\n  <!-- Bouton retour -->\n  <button (click)=\"router.navigate(['/reunions'])\"\n          class=\"mb-4 flex items-center text-purple-600 hover:text-purple-800\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n    </svg>\n    Retour aux réunions\n  </button>\n\n  <!-- Chargement -->\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n  </div>\n\n  <!-- Erreur -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    {{ error }}\n  </div>\n\n  <!-- Détails de la réunion -->\n  <div *ngIf=\"!loading && reunion\" class=\"bg-white rounded-lg shadow-md p-6\">\n    <!-- Titre de la réunion -->\n    <div class=\"flex justify-between items-start mb-4\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-800\">{{ reunion.titre }}</h1>\n        <p class=\"text-gray-600 mt-1\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n      </div>\n      <button (click)=\"editReunion()\" class=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\">\n        Modifier\n      </button>\n    </div>\n\n    <!-- Date et heure -->\n    <div class=\"mb-6\">\n      <div class=\"flex items-center mb-2\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span class=\"text-gray-700\">\n          {{ reunion.date | date:'fullDate' }}, {{ reunion.heureDebut }} - {{ reunion.heureFin }}\n        </span>\n      </div>\n    </div>\n\n    <!-- Créateur -->\n    <div class=\"mb-4\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Créateur:</h2>\n      <div class=\"flex items-center\">\n        <span class=\"text-gray-700\">{{ reunion.createur?.username }} ({{ reunion.createur?.email }})</span>\n      </div>\n    </div>\n\n    <!-- Participants -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Participants:</h2>\n      <ul class=\"list-disc pl-5\">\n        <li *ngFor=\"let participant of reunion.participants\" class=\"text-gray-700\">\n          {{ participant.username }} ({{ participant.email }})\n        </li>\n      </ul>\n    </div>\n\n    <!-- Planning -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Planning:</h2>\n      <div class=\"text-gray-700\">\n        <p>{{ reunion.planning?.titre }}</p>\n        <p>Du {{ reunion.planning?.dateDebut | date:'mediumDate' }} au {{ reunion.planning?.dateFin | date:'mediumDate' }}</p>\n      </div>\n    </div>\n\n    <!-- Lieu -->\n    <div *ngIf=\"reunion.lieu\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lieu:</h2>\n      <div class=\"flex items-center\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n        <span class=\"text-gray-700\">{{ reunion.lieu }}</span>\n      </div>\n    </div>\n\n    <!-- Lien Visio -->\n    <div *ngIf=\"reunion.lienVisio\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lien Visio:</h2>\n      <a [href]=\"reunion.lienVisio\" class=\"text-blue-600 hover:underline flex items-center\" target=\"_blank\">\n        <svg class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n        Rejoindre la réunion\n      </a>\n    </div>\n  </div>\n</div>"], "mappings": ";;;;;;;ICWEA,EAAA,CAAAC,eAAA,EAA8C;IAA9CD,EAAA,CAAAE,cAAA,aAA8C;IAC5CF,EAAA,CAAAG,SAAA,aAA4F;IAC9FH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAGNJ,EAAA,CAAAC,eAAA,EAAgG;IAAhGD,EAAA,CAAAE,cAAA,aAAgG;IAC9FF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAuCMT,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,cAAA,CAAAC,QAAA,QAAAD,cAAA,CAAAE,KAAA,OACF;;;;;IAcJb,EAAA,CAAAE,cAAA,cAAuC;IACgBF,EAAA,CAAAK,MAAA,YAAK;IAAAL,EAAA,CAAAI,YAAA,EAAK;IAC/DJ,EAAA,CAAAE,cAAA,cAA+B;IAC7BF,EAAA,CAAAc,cAAA,EAA8F;IAA9Fd,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAG,SAAA,eAA+J;IAEjKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAK,MAAA,GAAkB;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAAzBJ,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAKlDlB,EAAA,CAAAE,cAAA,cAA4C;IACWF,EAAA,CAAAK,MAAA,kBAAW;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAE,cAAA,YAAsG;IACpGF,EAAA,CAAAc,cAAA,EAAgF;IAAhFd,EAAA,CAAAE,cAAA,cAAgF;IAC9EF,EAAA,CAAAG,SAAA,eAA+M;IACjNH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,MAAA,kCACF;IAAAL,EAAA,CAAAI,YAAA,EAAI;;;;IALDJ,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAH,OAAA,CAAAI,SAAA,EAAArB,EAAA,CAAAsB,aAAA,CAA0B;;;;;;;IAlEjCtB,EAAA,CAAAC,eAAA,EAA2E;IAA3ED,EAAA,CAAAE,cAAA,cAA2E;IAIxBF,EAAA,CAAAK,MAAA,GAAmB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAG,SAAA,YAAwF;;IAC1FH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBAAqH;IAA7GF,EAAA,CAAAuB,UAAA,mBAAAC,8DAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7B9B,EAAA,CAAAK,MAAA,iBACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAIXJ,EAAA,CAAAE,cAAA,cAAkB;IAEdF,EAAA,CAAAc,cAAA,EAA8F;IAA9Fd,EAAA,CAAAE,cAAA,eAA8F;IAC5FF,EAAA,CAAAG,SAAA,gBAAmK;IACrKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,gBAA4B;IAC1BF,EAAA,CAAAK,MAAA,IACF;;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAKXJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,sBAAS;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAE,cAAA,eAA+B;IACDF,EAAA,CAAAK,MAAA,IAAgE;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAKvGJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,qBAAa;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACvEJ,EAAA,CAAAE,cAAA,cAA2B;IACzBF,EAAA,CAAA+B,UAAA,KAAAC,2CAAA,iBAEK;IACPhC,EAAA,CAAAI,YAAA,EAAK;IAIPJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,iBAAS;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAE,cAAA,eAA2B;IACtBF,EAAA,CAAAK,MAAA,IAA6B;IAAAL,EAAA,CAAAI,YAAA,EAAI;IACpCJ,EAAA,CAAAE,cAAA,SAAG;IAAAF,EAAA,CAAAK,MAAA,IAA+G;;;IAAAL,EAAA,CAAAI,YAAA,EAAI;IAK1HJ,EAAA,CAAA+B,UAAA,KAAAE,4CAAA,kBASM;IAGNjC,EAAA,CAAA+B,UAAA,KAAAG,4CAAA,kBAQM;IACRlC,EAAA,CAAAI,YAAA,EAAM;;;;IArE6CJ,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAe,iBAAA,CAAAoB,MAAA,CAAAlB,OAAA,CAAAmB,KAAA,CAAmB;IAClCpC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAmB,UAAA,cAAAnB,EAAA,CAAAqC,WAAA,QAAAF,MAAA,CAAAlB,OAAA,CAAAqB,WAAA,GAAAtC,EAAA,CAAAuC,cAAA,CAAqD;IAcjFvC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAwC,kBAAA,MAAAxC,EAAA,CAAAyC,WAAA,SAAAN,MAAA,CAAAlB,OAAA,CAAAyB,IAAA,qBAAAP,MAAA,CAAAlB,OAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAlB,OAAA,CAAA2B,QAAA,MACF;IAQ4B5C,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAU,kBAAA,KAAAyB,MAAA,CAAAlB,OAAA,CAAA4B,QAAA,kBAAAV,MAAA,CAAAlB,OAAA,CAAA4B,QAAA,CAAAjC,QAAA,QAAAuB,MAAA,CAAAlB,OAAA,CAAA4B,QAAA,kBAAAV,MAAA,CAAAlB,OAAA,CAAA4B,QAAA,CAAAhC,KAAA,MAAgE;IAQhEb,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmB,UAAA,YAAAgB,MAAA,CAAAlB,OAAA,CAAA6B,YAAA,CAAuB;IAUhD9C,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAe,iBAAA,CAAAoB,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,kBAAAZ,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,CAAAX,KAAA,CAA6B;IAC7BpC,EAAA,CAAAM,SAAA,GAA+G;IAA/GN,EAAA,CAAAU,kBAAA,QAAAV,EAAA,CAAAyC,WAAA,SAAAN,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,kBAAAZ,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,CAAAC,SAAA,yBAAAhD,EAAA,CAAAyC,WAAA,SAAAN,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,kBAAAZ,MAAA,CAAAlB,OAAA,CAAA8B,QAAA,CAAAE,OAAA,oBAA+G;IAKhHjD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,UAAA,SAAAgB,MAAA,CAAAlB,OAAA,CAAAC,IAAA,CAAkB;IAYlBlB,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmB,UAAA,SAAAgB,MAAA,CAAAlB,OAAA,CAAAI,SAAA,CAAuB;;;ADrDjC,OAAM,MAAO6B,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,cAA8B;IAD9B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IANxB,KAAApC,OAAO,GAAmB,IAAI;IAC9B,KAAAqC,OAAO,GAAG,IAAI;IACd,KAAA7C,KAAK,GAAkB,IAAI;EAKxB;EAEH8C,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC7C,KAAK,GAAG,IAAI;IAEjB,MAAMgD,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAAChD,KAAK,GAAG,wBAAwB;MACrC,IAAI,CAAC6C,OAAO,GAAG,KAAK;MACpB;;IAGF;IACAO,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC5C,OAAO,GAAG;QACb6C,GAAG,EAAEL,EAAE;QACPrB,KAAK,EAAE,mBAAmB;QAC1BE,WAAW,EACT,gEAAgE;QAClEU,SAAS,EAAE,IAAIe,IAAI,CAAC,qBAAqB,CAAC;QAC1Cd,OAAO,EAAE,IAAIc,IAAI,CAAC,qBAAqB,CAAC;QACxC7C,IAAI,EAAE,uBAAuB;QAC7BG,SAAS,EAAE,sCAAsC;QACjD2C,UAAU,EAAE;UACVF,GAAG,EAAE,aAAa;UAClB1B,KAAK,EAAE;SACR;QACDU,YAAY,EAAE,CACZ;UACEgB,GAAG,EAAE,OAAO;UACZlD,QAAQ,EAAE,aAAa;UACvBqD,KAAK,EAAE;SACR,EACD;UACEH,GAAG,EAAE,OAAO;UACZlD,QAAQ,EAAE,cAAc;UACxBqD,KAAK,EAAE;SACR;OAEJ;MAED,IAAI,CAACX,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAzDWJ,sBAAsB,EAAAlD,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAtBpB,sBAAsB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCnC7E,EAAA,CAAAE,cAAA,aAAyC;UAE/BF,EAAA,CAAAuB,UAAA,mBAAAwD,wDAAA;YAAA,OAASD,GAAA,CAAAE,MAAA,CAAAC,QAAA,EAAiB,WAAW,EAAE;UAAA,EAAC;UAE9CjF,EAAA,CAAAc,cAAA,EAAqG;UAArGd,EAAA,CAAAE,cAAA,aAAqG;UACnGF,EAAA,CAAAG,SAAA,cAA0L;UAC5LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,MAAA,iCACF;UAAAL,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAA+B,UAAA,IAAAmD,qCAAA,iBAEM;UAGNlF,EAAA,CAAA+B,UAAA,IAAAoD,qCAAA,iBAEM;UAGNnF,EAAA,CAAA+B,UAAA,IAAAqD,qCAAA,mBAyEM;UACRpF,EAAA,CAAAI,YAAA,EAAM;;;UApFEJ,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAmB,UAAA,SAAA2D,GAAA,CAAAxB,OAAA,CAAa;UAKbtD,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAmB,UAAA,SAAA2D,GAAA,CAAArE,KAAA,CAAW;UAKXT,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAmB,UAAA,UAAA2D,GAAA,CAAAxB,OAAA,IAAAwB,GAAA,CAAA7D,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}