{"ast": null, "code": "import { CalendarView } from 'angular-calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/authuser.service\";\nexport class PlanningDetailComponent {\n  constructor(route, router, planningService, authService, cdr) {\n    this.route = route;\n    this.router = router;\n    this.planningService = planningService;\n    this.authService = authService;\n    this.cdr = cdr;\n    this.planning = null;\n    this.loading = true;\n    this.error = null;\n    this.isCreator = false;\n    this.selectedDayEvents = [];\n    this.selectedDate = null;\n    // Calendar setup\n    this.view = CalendarView.Month;\n    this.viewDate = new Date();\n    this.events = [];\n  }\n  ngOnInit() {\n    this.loadPlanningDetails();\n  }\n  loadPlanningDetails() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de planning non fourni';\n      this.loading = false;\n      return;\n    }\n    this.planningService.getPlanningById(id).subscribe({\n      next: planning => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n        this.events = this.planning.reunions.map(reunion => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false\n          };\n        });\n        this.cdr.detectChanges();\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n  handleDayClick(day) {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events; // These come from your `events` array\n  }\n\n  editPlanning() {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n  deletePlanning() {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => this.router.navigate(['/plannings']),\n        error: err => this.error = err.error?.message || 'Erreur lors de la suppression'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningDetailComponent_Factory(t) {\n      return new (t || PlanningDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningDetailComponent,\n      selectors: [[\"app-planning-detail\"]],\n      decls: 0,\n      vars: 0,\n      template: function PlanningDetailComponent_Template(rf, ctx) {},\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWRldGFpbC9wbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CalendarView", "PlanningDetailComponent", "constructor", "route", "router", "planningService", "authService", "cdr", "planning", "loading", "error", "isCreator", "selectedDayEvents", "selectedDate", "view", "Month", "viewDate", "Date", "events", "ngOnInit", "loadPlanningDetails", "id", "snapshot", "paramMap", "get", "getPlanningById", "subscribe", "next", "<PERSON>ur", "_id", "getCurrentUserId", "reunions", "map", "reunion", "startStr", "date", "substring", "heureDebut", "endStr", "heure<PERSON>in", "start", "end", "title", "titre", "allDay", "detectChanges", "err", "message", "console", "handleDayClick", "day", "editPlanning", "navigate", "deletePlanning", "confirm", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "PlanningService", "i3", "AuthuserService", "ChangeDetectorRef", "selectors", "decls", "vars", "template", "PlanningDetailComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-detail\\planning-detail.component.ts"], "sourcesContent": ["import {ChangeDetectorRef, Component, OnInit} from '@angular/core';\n\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from '@app/services/authuser.service';\nimport { PlanningService } from '@app/services/planning.service';\nimport {\n  CalendarEvent, CalendarMonthViewDay,\n  CalendarView,\n} from 'angular-calendar';\n\n\n@Component({\n  selector: 'app-planning-detail',\n  templateUrl: './planning-detail.component.html',\n  styleUrls: ['./planning-detail.component.css']\n})\nexport class PlanningDetailComponent implements OnInit {\n\n  planning: any | null = null;\n  loading = true;\n  error: string | null = null;\n  isCreator = false;\n  selectedDayEvents: CalendarEvent[] = [];\n  selectedDate: Date | null = null;\n\n  // Calendar setup\n  view: CalendarView = CalendarView.Month;\n  viewDate: Date = new Date();\n  events: CalendarEvent[] = [];\n\n  constructor(\n    public route: ActivatedRoute,\n    public router: Router,\n    private planningService: PlanningService,\n    public authService: AuthuserService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPlanningDetails();\n  }\n\n  loadPlanningDetails(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de planning non fourni';\n      this.loading = false;\n      return;\n    }\n\n    this.planningService.getPlanningById(id).subscribe({\n      next: (planning: any) => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n\n        this.events = this.planning.reunions.map((reunion: any) => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false\n          };\n        });\n\n        this.cdr.detectChanges();\n      },\n      error: (err: any) => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n\n  handleDayClick(day: CalendarMonthViewDay): void {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events; // These come from your `events` array\n  }\n\n\n  editPlanning(): void {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n\n  deletePlanning(): void {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => this.router.navigate(['/plannings']),\n        error: (err) => this.error = err.error?.message || 'Erreur lors de la suppression'\n      });\n    }\n  }\n}"], "mappings": "AAKA,SAEEA,YAAY,QACP,kBAAkB;;;;;AAQzB,OAAM,MAAOC,uBAAuB;EAclCC,YACSC,KAAqB,EACrBC,MAAc,EACbC,eAAgC,EACjCC,WAA4B,EAC3BC,GAAsB;IAJvB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,GAAG,GAAHA,GAAG;IAjBb,KAAAC,QAAQ,GAAe,IAAI;IAC3B,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,iBAAiB,GAAoB,EAAE;IACvC,KAAAC,YAAY,GAAgB,IAAI;IAEhC;IACA,KAAAC,IAAI,GAAiBd,YAAY,CAACe,KAAK;IACvC,KAAAC,QAAQ,GAAS,IAAIC,IAAI,EAAE;IAC3B,KAAAC,MAAM,GAAoB,EAAE;EAQzB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,EAAE,GAAG,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAACX,KAAK,GAAG,2BAA2B;MACxC,IAAI,CAACD,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACJ,eAAe,CAACoB,eAAe,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MACjDC,IAAI,EAAGnB,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;QACjC,IAAI,CAACG,SAAS,GAAGH,QAAQ,CAACA,QAAQ,CAACoB,QAAQ,CAACC,GAAG,KAAK,IAAI,CAACvB,WAAW,CAACwB,gBAAgB,EAAE;QACvF,IAAI,CAACrB,OAAO,GAAG,KAAK;QAEpB,IAAI,CAACS,MAAM,GAAG,IAAI,CAACV,QAAQ,CAACuB,QAAQ,CAACC,GAAG,CAAEC,OAAY,IAAI;UACxD,MAAMC,QAAQ,GAAG,GAAGD,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACI,UAAU,KAAK;UAC5E,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACM,QAAQ,KAAK;UAExE,OAAO;YACLC,KAAK,EAAE,IAAIvB,IAAI,CAACiB,QAAQ,CAAC;YACzBO,GAAG,EAAE,IAAIxB,IAAI,CAACqB,MAAM,CAAC;YACrBI,KAAK,EAAET,OAAO,CAACU,KAAK;YACpBC,MAAM,EAAE;WACT;QACH,CAAC,CAAC;QAEF,IAAI,CAACrC,GAAG,CAACsC,aAAa,EAAE;MAC1B,CAAC;MACDnC,KAAK,EAAGoC,GAAQ,IAAI;QAClB,IAAI,CAACpC,KAAK,GAAGoC,GAAG,CAACpC,KAAK,EAAEqC,OAAO,IAAI,2BAA2B;QAC9D,IAAI,CAACtC,OAAO,GAAG,KAAK;QACpBuC,OAAO,CAACtC,KAAK,CAAC,SAAS,EAAEoC,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEAG,cAAcA,CAACC,GAAyB;IACtC,IAAI,CAACrC,YAAY,GAAGqC,GAAG,CAACf,IAAI;IAC5B,IAAI,CAACvB,iBAAiB,GAAGsC,GAAG,CAAChC,MAAM,CAAC,CAAC;EACvC;;EAGAiC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC3C,QAAQ,EAAE;MACjB,IAAI,CAACJ,MAAM,CAACgD,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC5C,QAAQ,CAACqB,GAAG,CAAC,CAAC;;EAEhE;EAEAwB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7C,QAAQ,IAAI8C,OAAO,CAAC,wCAAwC,CAAC,EAAE;MACtE,IAAI,CAACjD,eAAe,CAACgD,cAAc,CAAC,IAAI,CAAC7C,QAAQ,CAACqB,GAAG,CAAC,CAACH,SAAS,CAAC;QAC/DC,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACvB,MAAM,CAACgD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QAChD1C,KAAK,EAAGoC,GAAG,IAAK,IAAI,CAACpC,KAAK,GAAGoC,GAAG,CAACpC,KAAK,EAAEqC,OAAO,IAAI;OACpD,CAAC;;EAEN;;;uBAjFW9C,uBAAuB,EAAAsD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAS,iBAAA;IAAA;EAAA;;;YAAvB/D,uBAAuB;MAAAgE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}