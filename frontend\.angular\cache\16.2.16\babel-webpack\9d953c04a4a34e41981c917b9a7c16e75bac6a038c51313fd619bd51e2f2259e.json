{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/planning.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nexport class PlanningListComponent {\n  constructor(planningService, authService, router, route) {\n    this.planningService = planningService;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.plannings = [];\n    this.loading = true;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadPlannings();\n  }\n  loadPlannings() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) {\n      this.error = 'Utilisateur non connecté';\n      this.loading = false;\n      return;\n    }\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: response => {\n        if (response.success) {\n          this.plannings = response.plannings;\n          console.log(this.plannings);\n        } else {\n          this.error = 'Erreur lors du chargement';\n        }\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur serveur';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n  deletePlanning(id) {\n    if (confirm('Supprimer ce planning ?')) {\n      this.planningService.deletePlanning(id).subscribe({\n        next: () => {\n          this.plannings = this.plannings.filter(p => p._id !== id);\n        },\n        error: err => {\n          this.error = err.error?.message || 'Erreur lors de la suppression';\n        }\n      });\n    }\n  }\n  GotoDetail(id) {\n    if (id) {\n      this.router.navigate([id], {\n        relativeTo: this.route\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningListComponent_Factory(t) {\n      return new (t || PlanningListComponent)(i0.ɵɵdirectiveInject(i1.PlanningService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningListComponent,\n      selectors: [[\"app-planning-list\"]],\n      decls: 0,\n      vars: 0,\n      template: function PlanningListComponent_Template(rf, ctx) {},\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1saXN0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWxpc3QvcGxhbm5pbmctbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PlanningListComponent", "constructor", "planningService", "authService", "router", "route", "plannings", "loading", "error", "ngOnInit", "loadPlannings", "userId", "getCurrentUserId", "getPlanningsByUser", "subscribe", "next", "response", "success", "console", "log", "err", "message", "deletePlanning", "id", "confirm", "filter", "p", "_id", "GotoDetail", "navigate", "relativeTo", "i0", "ɵɵdirectiveInject", "i1", "PlanningService", "i2", "AuthuserService", "i3", "Router", "ActivatedRoute", "selectors", "decls", "vars", "template", "PlanningListComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-list\\planning-list.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ReunionService } from 'src/app/services/reunion.service';\nimport { PlanningService } from 'src/app/services/planning.service';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { Planning } from 'src/app/models/planning.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport {ActivatedRoute, Router} from \"@angular/router\";\n\n@Component({\n  selector: 'app-planning-list',\n  templateUrl: './planning-list.component.html',\n  styleUrls: ['./planning-list.component.css']\n})\nexport class PlanningListComponent implements OnInit {\n  plannings: Planning[] = [];\n  loading = true;\n  error: string | null = null;\n\n  constructor(\n    private planningService: PlanningService,\n    public authService: AuthuserService,\n    private router: Router, private route: ActivatedRoute\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPlannings();\n  }\n\n  loadPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) {\n      this.error = 'Utilisateur non connecté';\n      this.loading = false;\n      return;\n    }\n\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: (response: any) => {\n        if (response.success) {\n          this.plannings = response.plannings;\n          console.log(this.plannings)\n        } else {\n          this.error = 'Erreur lors du chargement';\n        }\n        this.loading = false;\n      },\n      error: (err) => {\n        this.error = err.error?.message || 'Erreur serveur';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n\n  deletePlanning(id: string): void {\n    if (confirm('Supprimer ce planning ?')) {\n      this.planningService.deletePlanning(id).subscribe({\n        next: () => {\n          this.plannings = this.plannings.filter(p => p._id !== id);\n        },\n        error: (err) => {\n          this.error = err.error?.message || 'Erreur lors de la suppression';\n        }\n      });\n    }\n  }\n\n  GotoDetail(id: string | undefined) {\n    if (id) {\n      this.router.navigate([id], { relativeTo: this.route });\n    }\n  }\n\n}"], "mappings": ";;;;AAaA,OAAM,MAAOA,qBAAqB;EAKhCC,YACUC,eAAgC,EACjCC,WAA4B,EAC3BC,MAAc,EAAUC,KAAqB;IAF7C,KAAAH,eAAe,GAAfA,eAAe;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,KAAK,GAALA,KAAK;IAPvC,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,KAAK,GAAkB,IAAI;EAMxB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,MAAMC,MAAM,GAAG,IAAI,CAACR,WAAW,CAACS,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;MACX,IAAI,CAACH,KAAK,GAAG,0BAA0B;MACvC,IAAI,CAACD,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACL,eAAe,CAACW,kBAAkB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACX,SAAS,GAAGU,QAAQ,CAACV,SAAS;UACnCY,OAAO,CAACC,GAAG,CAAC,IAAI,CAACb,SAAS,CAAC;SAC5B,MAAM;UACL,IAAI,CAACE,KAAK,GAAG,2BAA2B;;QAE1C,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGY,GAAG,IAAI;QACb,IAAI,CAACZ,KAAK,GAAGY,GAAG,CAACZ,KAAK,EAAEa,OAAO,IAAI,gBAAgB;QACnD,IAAI,CAACd,OAAO,GAAG,KAAK;QACpBW,OAAO,CAACV,KAAK,CAAC,SAAS,EAAEY,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEAE,cAAcA,CAACC,EAAU;IACvB,IAAIC,OAAO,CAAC,yBAAyB,CAAC,EAAE;MACtC,IAAI,CAACtB,eAAe,CAACoB,cAAc,CAACC,EAAE,CAAC,CAACT,SAAS,CAAC;QAChDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,SAAS,GAAG,IAAI,CAACA,SAAS,CAACmB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,EAAE,CAAC;QAC3D,CAAC;QACDf,KAAK,EAAGY,GAAG,IAAI;UACb,IAAI,CAACZ,KAAK,GAAGY,GAAG,CAACZ,KAAK,EAAEa,OAAO,IAAI,+BAA+B;QACpE;OACD,CAAC;;EAEN;EAEAO,UAAUA,CAACL,EAAsB;IAC/B,IAAIA,EAAE,EAAE;MACN,IAAI,CAACnB,MAAM,CAACyB,QAAQ,CAAC,CAACN,EAAE,CAAC,EAAE;QAAEO,UAAU,EAAE,IAAI,CAACzB;MAAK,CAAE,CAAC;;EAE1D;;;uBA1DWL,qBAAqB,EAAA+B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAArBvC,qBAAqB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}